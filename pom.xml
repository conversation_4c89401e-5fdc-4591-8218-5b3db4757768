<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.afakto</groupId>
    <artifactId>afakto</artifactId>
    <version>20250625</version>
    <packaging>jar</packaging>
    <name>Afakto</name>
    <description>A factoring web application</description>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath />
        <!-- lookup parent from repository -->
    </parent>

    <properties>
        <!--
            Build properties
        The spring-boot version should match the one managed by
        https://mvnrepository.com/artifact/tech.jhipster/jhipster-dependencies/${jhipster-dependencies.version}
        -->
        <maven.version>3.8.5</maven.version>
        <java.version>21</java.version>
        <node.version>v22.15.0</node.version>
        <npm.version>11.3.0</npm.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <start-class>com.afakto.AfaktoApp</start-class>
        <argLine>-Djava.security.egd=file:/dev/./urandom -Xmx1G</argLine>
        <m2e.apt.activation>jdt_apt</m2e.apt.activation>
        <run.addResources>false</run.addResources>
        <spring-boot.version>${project.parent.version}</spring-boot.version>
        <archunit-junit5.version>1.4.1</archunit-junit5.version>
        <checkstyle.version>10.26.0</checkstyle.version>
        <checksum-maven-plugin.version>1.11</checksum-maven-plugin.version>
        <frontend-maven-plugin.version>1.15.1</frontend-maven-plugin.version>
        <git-commit-id-maven-plugin.version>9.0.2</git-commit-id-maven-plugin.version>
        <jhipster-framework.version>8.11.0</jhipster-framework.version>
        <jib-maven-plugin.architecture>amd64</jib-maven-plugin.architecture>
        <jib-maven-plugin.image>eclipse-temurin:17-jre-focal</jib-maven-plugin.image>
        <jib-maven-plugin.version>3.4.6</jib-maven-plugin.version>
        <lifecycle-mapping.version>1.0.0</lifecycle-mapping.version>
        <liquibase-plugin.password />
        <liquibase-plugin.url />
        <liquibase-plugin.username />
        <mapstruct.version>1.6.3</mapstruct.version>
        <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <maven-clean-plugin.version>3.5.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
        <maven-enforcer-plugin.version>3.5.0</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>3.5.3</maven-failsafe-plugin.version>
        <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.11.2</maven-javadoc-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-site-plugin.version>3.21.0</maven-site-plugin.version>
        <maven-surefire-plugin.version>3.5.3</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
        <modernizer-maven-plugin.version>3.1.0</modernizer-maven-plugin.version>
        <nohttp-checkstyle.version>0.0.11</nohttp-checkstyle.version>
        <profile.api-docs />
        <profile.e2e />
        <profile.no-liquibase />
        <profile.test />
        <profile.tls />
        <properties-maven-plugin.version>1.2.1</properties-maven-plugin.version>
        <resource.delimiter>@</resource.delimiter>
        <sonar-maven-plugin.version>5.1.0.4751</sonar-maven-plugin.version>
        <spotless-maven-plugin.version>2.44.5</spotless-maven-plugin.version>
        <spring.profiles.active>dev</spring.profiles.active>
        <springdoc-openapi-starter-webmvc-api.version>2.8.9</springdoc-openapi-starter-webmvc-api.version>
        <springdoc-openapi-starter-webmvc-ui.version>2.8.9</springdoc-openapi-starter-webmvc-ui.version>

        <liquibase.version>4.30.0</liquibase.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>tech.jhipster</groupId>
            <artifactId>jhipster-framework</artifactId>
            <version>${jhipster-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-loader-tools</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <!-- Spring Security OAuth 2.0 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-data</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
            <version>${springdoc-openapi-starter-webmvc-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi-starter-webmvc-ui.version}</version>
        </dependency>
        <!-- Therapi JavaDoc parser for SpringDoc to read JavaDoc comments -->
        <dependency>
            <groupId>com.github.therapi</groupId>
            <artifactId>therapi-runtime-javadoc</artifactId>
            <version>0.15.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate6</artifactId>
        </dependency>
        <!--  Add-on module to support JSR-310 (Java 8 Date & Time API) data types. -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.batch</groupId>
            <artifactId>spring-batch-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5-api</artifactId>
            <version>${archunit-junit5.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <!-- Adding the engine dependency to the surefire-plugin unfortunately does not work in
            the current version. -->
            <!-- https://www.archunit.org/userguide/html/000_Index.html#_junit_5 -->
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5-engine</artifactId>
            <version>${archunit-junit5.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <classifier>jakarta</classifier>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jcache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jpamodelgen</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-envers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!--
        https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>4.3.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.github.openfeign/feign-okhttp -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
            <version>13.6</version>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>auth0</artifactId>
            <version>2.22.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-csv -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.14.0</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.19.0</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
            <version>77.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>3.10.1</version>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>jackson-datatype-problem</artifactId>
            <version>0.27.1</version>
        </dependency>
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web</artifactId>
            <version>0.29.1</version>
        </dependency>
        <dependency>
            <groupId>javax.money</groupId>
            <artifactId>money-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>org.javamoney</groupId>
            <artifactId>moneta</artifactId>
            <version>1.4.5</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.prowidesoftware</groupId>
            <artifactId>pw-swift-core</artifactId>
            <version>SRU2024-10.2.8</version>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.4.8-jre</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.27.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>spring-boot:run</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources/</directory>
                <filtering>true</filtering>
                <includes>
                    <include>config/**/*.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources/</directory>
                <excludes>
                    <exclude>config/**/*.yml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.gaul</groupId>
                <artifactId>modernizer-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>4.9.3.1</version>
                <configuration>
                    <excludeFilterFile>${project.basedir}/.spotbugs/excludeFilter.xml</excludeFilterFile>
                    <plugins>
                        <plugin>
                            <groupId>com.h3xstream.findsecbugs</groupId>
                            <artifactId>findsecbugs-plugin</artifactId>
                            <version>1.12.0</version>
                        </plugin>
                    </plugins>
                </configuration>
            </plugin>
            <plugin>
                <groupId>se.bjurr.gitchangelog</groupId>
                <artifactId>git-changelog-maven-plugin</artifactId>
                <version>2.2.5</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>git-changelog</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>io.spring.nohttp</groupId>
                            <artifactId>nohttp-checkstyle</artifactId>
                            <version>${nohttp-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>checkstyle.xml</configLocation>
                        <includes>pom.xml,README.md</includes>
                        <excludes>.git/**/*,target/**/*,node_modules/**/*</excludes>
                        <sourceDirectories>./</sourceDirectories>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <parameters>true</parameters>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.hibernate.orm</groupId>
                                <artifactId>hibernate-jpamodelgen</artifactId>
                            </path>
                            <path>
                                <groupId>org.glassfish.jaxb</groupId>
                                <artifactId>jaxb-runtime</artifactId>
                            </path>
                            <path>
                                <groupId>com.github.therapi</groupId>
                                <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                                <version>0.15.0</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>enforce-versions</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>enforce-dependencyConvergence</id>
                            <configuration>
                                <rules>
                                    <DependencyConvergence />
                                </rules>
                                <fail>false</fail>
                            </configuration>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <rules>
                            <requireMavenVersion>
                                <message>You are running an older version of Maven. JHipster
                                    requires at least Maven ${maven.version}</message>
                                <version>[${maven.version},)</version>
                            </requireMavenVersion>
                            <requireJavaVersion>
                                <message>You are running an incompatible version of Java. JHipster
                                    supports JDK 17 to 24.</message>
                                <version>[17,18),[21,22),[24,25)</version>
                            </requireJavaVersion>
                        </rules>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                    <configuration>
                        <!-- Due to spring-boot repackage, without adding this property test classes
                        are not found
                        See https://github.com/spring-projects/spring-boot/issues/6254 -->
                        <!-- Force alphabetical order to have a reproducible build -->
                        <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                        <runOrder>alphabetical</runOrder>
                        <includes>
                            <include>**/*IT*</include>
                            <include>**/*IntTest*</include>
                        </includes>
                        <argLine>@{argLine} -Dspring.profiles.active=${profile.test}</argLine>
                    </configuration>
                    <executions>
                        <execution>
                            <id>integration-test</id>
                            <goals>
                                <goal>integration-test</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>verify</id>
                            <goals>
                                <goal>verify</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <!-- Force alphabetical order to have a reproducible build -->
                        <runOrder>alphabetical</runOrder>
                        <excludes>
                            <exclude>**/*IT*</exclude>
                            <exclude>**/*IntTest*</exclude>
                        </excludes>
                        <systemPropertyVariables>
                            <java.util.logging.config.file>src/test/resources/logback.xml</java.util.logging.config.file>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven-war-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default-war</id>
                            <goals>
                                <goal>war</goal>
                            </goals>
                            <phase>package</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <warSourceIncludes>WEB-INF/**,META-INF/**</warSourceIncludes>
                        <failOnMissingWebXml>false</failOnMissingWebXml>
                        <warSourceDirectory>target/classes/static/</warSourceDirectory>
                        <webResources>
                            <resource>
                                <directory>src/main/webapp</directory>
                                <includes>
                                    <include>WEB-INF/**</include>
                                </includes>
                            </resource>
                        </webResources>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>build-info</id>
                            <goals>
                                <goal>build-info</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>repackage</id>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <mainClass>${start-class}</mainClass>
                        <!--
                            Enable the line below to have remote debugging of your application on port 5005
                            <jvmArguments>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005</jvmArguments>
                        -->
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless-maven-plugin.version}</version>
                    <configuration>
                        <java>
                            <removeUnusedImports />
                        </java>
                    </configuration>
                    <executions>
                        <execution>
                            <id>spotless</id>
                            <phase>process-sources</phase>
                            <goals>
                                <goal>apply</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.github.eirslett</groupId>
                    <artifactId>frontend-maven-plugin</artifactId>
                    <version>${frontend-maven-plugin.version}</version>
                    <configuration>
                        <installDirectory>target</installDirectory>
                        <nodeVersion>${node.version}</nodeVersion>
                        <npmVersion>${npm.version}</npmVersion>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>${jib-maven-plugin.version}</version>
                    <configuration>
                        <from>
                            <image>${jib-maven-plugin.image}</image>
                            <platforms>
                                <platform>
                                    <architecture>${jib-maven-plugin.architecture}</architecture>
                                    <os>linux</os>
                                </platform>
                            </platforms>
                        </from>
                        <to>
                            <image>afakto:latest</image>
                        </to>
                        <container>
                            <entrypoint>
                                <shell>bash</shell>
                                <option>-c</option>
                                <arg>/entrypoint.sh</arg>
                            </entrypoint>
                            <ports>
                                <port>8080</port>
                            </ports>
                            <environment>
                                <SPRING_OUTPUT_ANSI_ENABLED>ALWAYS</SPRING_OUTPUT_ANSI_ENABLED>
                                <JHIPSTER_SLEEP>0</JHIPSTER_SLEEP>
                            </environment>
                            <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                            <user>1000</user>
                        </container>
                        <extraDirectories>
                            <paths>src/main/docker/jib</paths>
                            <permissions>
                                <permission>
                                    <file>/entrypoint.sh</file>
                                    <mode>755</mode>
                                </permission>
                            </permissions>
                        </extraDirectories>
                        <pluginExtensions>
                            <pluginExtension>
                                <implementation>
                                    com.google.cloud.tools.jib.maven.extension.springboot.JibSpringBootExtension</implementation>
                            </pluginExtension>
                        </pluginExtensions>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>com.google.cloud.tools</groupId>
                            <artifactId>jib-spring-boot-extension-maven</artifactId>
                            <version>0.1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <version>${git-commit-id-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <failOnNoGitDirectory>false</failOnNoGitDirectory>
                        <failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <includeOnlyProperties>
                            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.describe$</includeOnlyProperty>
                            <includeOnlyProperty>^git.branch$</includeOnlyProperty>
                        </includeOnlyProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>net.nicoulaj.maven.plugins</groupId>
                    <artifactId>checksum-maven-plugin</artifactId>
                    <version>${checksum-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>${properties-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>initialize</phase>
                            <goals>
                                <goal>read-project-properties</goal>
                            </goals>
                            <configuration>
                                <files>
                                    <file>sonar-project.properties</file>
                                </files>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.gaul</groupId>
                    <artifactId>modernizer-maven-plugin</artifactId>
                    <version>${modernizer-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>modernizer</id>
                            <phase>package</phase>
                            <goals>
                                <goal>modernizer</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <javaVersion>${java.version}</javaVersion>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-maven-plugin</artifactId>
                    <version>${liquibase.version}</version>
                    <configuration>
                        <changeLogFile>config/liquibase/master.xml</changeLogFile>
                        <diffChangeLogFile>
                            ${project.basedir}/src/main/resources/config/liquibase/changelog/${maven.build.timestamp}_changelog.xml</diffChangeLogFile>
                        <driver>org.postgresql.Driver</driver>
                        <url>${liquibase-plugin.url}</url>
                        <defaultSchemaName />
                        <username>${liquibase-plugin.username}</username>
                        <password>${liquibase-plugin.password}</password>
                        <referenceUrl>
                            hibernate:spring:com.afakto.domain?dialect=org.hibernate.dialect.PostgreSQLDialect&amp;
                            hibernate.physical_naming_strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy&amp;
                            hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
                        </referenceUrl>
                        <verbose>true</verbose>
                        <logging>debug</logging>
                        <contexts>!test</contexts>
                        <diffExcludeObjects>oauth_access_token, oauth_approvals,
                            oauth_client_details, oauth_client_token, oauth_code,
                            oauth_refresh_token, batch_.*</diffExcludeObjects>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.liquibase</groupId>
                            <artifactId>liquibase-core</artifactId>
                            <version>${liquibase.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.liquibase.ext</groupId>
                            <artifactId>liquibase-hibernate6</artifactId>
                            <version>${liquibase.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>jakarta.validation</groupId>
                            <artifactId>jakarta.validation-api</artifactId>
                            <version>${jakarta-validation.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-data-jpa</artifactId>
                            <version>${spring-boot.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>api-docs</id>
            <properties>
                <profile.api-docs>,api-docs</profile.api-docs>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- default Spring profiles -->
                <liquibase-plugin.password>afakto</liquibase-plugin.password>
                <liquibase-plugin.url>*****************************************</liquibase-plugin.url>
                <liquibase-plugin.username>postgres</liquibase-plugin.username>
                <profile.test>test,testdev</profile.test>
                <spring.profiles.active>dev${profile.tls}${profile.no-liquibase}</spring.profiles.active>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <optional>true</optional>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>docker-compose</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-docker-compose</artifactId>
                    <optional>true</optional>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>e2e</id>
            <properties>
                <profile.e2e>,e2e</profile.e2e>
            </properties>
            <build>
                <finalName>e2e</finalName>
            </build>
        </profile>
        <profile>
            <!-- This is automatically activated when working in Eclipse -->
            <id>eclipse</id>
            <activation>
                <property>
                    <name>m2e.version</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <!-- The following dependency is added due to issue #9175-->
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-undertow</artifactId>
                </dependency>
            </dependencies>
            <build>
                <pluginManagement>
                    <plugins>
                        <!--
                            This plugin's configuration is used to store Eclipse m2e settings only.
                            It has no influence on the Maven build itself.
                            Remove when the m2e plugin can correctly bind to Maven lifecycle
                        -->
                        <plugin>
                            <groupId>org.eclipse.m2e</groupId>
                            <artifactId>lifecycle-mapping</artifactId>
                            <version>${lifecycle-mapping.version}</version>
                            <configuration>
                                <lifecycleMappingMetadata>
                                    <pluginExecutions>
                                        <pluginExecution>
                                            <pluginExecutionFilter>
                                                <groupId>com.github.eirslett</groupId>
                                                <artifactId>frontend-maven-plugin</artifactId>
                                                <versionRange>${frontend-maven-plugin.version}</versionRange>
                                                <goals>
                                                    <goal>install-node-and-npm</goal>
                                                    <goal>npm</goal>
                                                </goals>
                                            </pluginExecutionFilter>
                                            <action>
                                                <ignore />
                                            </action>
                                        </pluginExecution>
                                    </pluginExecutions>
                                </lifecycleMappingMetadata>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <!--
                Profile for applying IDE-specific configuration.
                At the moment it configures MapStruct and Hibernate JPA Metamodel Generator, which you need when
            working
                with DTOs and entity filtering.
            -->
            <id>IDE</id>
            <dependencies>
                <dependency>
                    <groupId>org.hibernate.orm</groupId>
                    <artifactId>hibernate-jpamodelgen</artifactId>
                </dependency>
                <dependency>
                    <groupId>org.mapstruct</groupId>
                    <artifactId>mapstruct-processor</artifactId>
                    <version>${mapstruct.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>no-liquibase</id>
            <properties>
                <profile.no-liquibase>,no-liquibase</profile.no-liquibase>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- default Spring profiles -->
                <spring.profiles.active>
                    prod${profile.api-docs}${profile.tls}${profile.e2e}${profile.no-liquibase}</spring.profiles.active>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-clean-plugin</artifactId>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>target/classes/static/</directory>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-node-and-npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>npm install</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>webapp build test</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>test</phase>
                                <configuration>
                                    <arguments>run webapp:test</arguments>
                                    <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
                                </configuration>
                            </execution>
                            <execution>
                                <id>webapp build prod</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run webapp:prod</arguments>
                                    <environmentVariables>
                                        <APP_VERSION>${project.version}</APP_VERSION>
                                    </environmentVariables>
                                    <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>tls</id>
            <properties>
                <profile.tls>,tls</profile.tls>
            </properties>
        </profile>
        <profile>
            <id>war</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>webapp</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!-- default Spring profiles -->
                <spring.profiles.active>dev${profile.no-liquibase}</spring.profiles.active>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>net.nicoulaj.maven.plugins</groupId>
                        <artifactId>checksum-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>create-pre-compiled-webapp-checksum</id>
                                <goals>
                                    <goal>files</goal>
                                </goals>
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>create-compiled-webapp-checksum</id>
                                <goals>
                                    <goal>files</goal>
                                </goals>
                                <phase>compile</phase>
                                <configuration>
                                    <csvSummaryFile>checksums.csv.old</csvSummaryFile>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <fileSets>
                                <fileSet>
                                    <directory>${project.basedir}</directory>
                                    <includes>
                                        <include>.postcssrc.js</include>
                                        <include>package-lock.json</include>
                                        <include>package.json</include>
                                        <include>src/main/webapp/**/*.*</include>
                                        <include>target/classes/static/**/*.*</include>
                                        <include>tsconfig.app.json</include>
                                        <include>vite.config.ts</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/app/**/service-worker.js</exclude>
                                        <exclude>**/app/**/vendor.css</exclude>
                                    </excludes>
                                </fileSet>
                            </fileSets>
                            <failOnError>false</failOnError>
                            <failIfNoFiles>false</failIfNoFiles>
                            <individualFiles>false</individualFiles>
                            <algorithms>
                                <algorithm>SHA-1</algorithm>
                            </algorithms>
                            <includeRelativePath>true</includeRelativePath>
                            <quiet>true</quiet>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>eval-frontend-checksum</id>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <condition property="skip.npm" value="true" else="false">
                                            <and>
                                                <available file="checksums.csv"
                                                    filepath="${project.build.directory}" />
                                                <available file="checksums.csv.old"
                                                    filepath="${project.build.directory}" />
                                                <filesmatch
                                                    file1="${project.build.directory}/checksums.csv"
                                                    file2="${project.build.directory}/checksums.csv.old" />
                                            </and>
                                        </condition>
                                    </target>
                                    <exportAntProperties>true</exportAntProperties>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>install-node-and-npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>npm install</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>webapp build dev</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run webapp:build</arguments>
                                    <environmentVariables>
                                        <APP_VERSION>${project.version}</APP_VERSION>
                                    </environmentVariables>
                                    <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
