package com.afakto.config;

import com.afakto.security.SecurityUtils;
import com.afakto.security.oauth2.JwtGrantedAuthorityConverter;
import com.afakto.web.filter.BypassAuthFilter;
import com.afakto.web.filter.SpaWebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer.FrameOptionsConfig;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUserAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.csrf.*;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.web.filter.CookieCsrfFilter;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Supplier;

import static com.afakto.security.AuthoritiesConstants.*;
import static org.springframework.security.config.Customizer.withDefaults;
import static org.springframework.security.oauth2.core.oidc.StandardClaimNames.PREFERRED_USERNAME;

@Configuration
@EnableMethodSecurity(securedEnabled = true)
@Import(SecurityProblemSupport.class)
@RequiredArgsConstructor
public class SecurityConfiguration {
    private final JHipsterProperties jHipsterProperties;
    private final SecurityProblemSupport problemSupport;
    @Value("${application.auth0-management.issuer-uri:}")
    private String issuer;
    @Value("${application.auth0-management.bypass:false}")
    private boolean authBypass;
    @Value("${application.auth0-management.bypass-user:}")
    private String bypassUser;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, MvcRequestMatcher.Builder mvc) throws Exception {
        http
            .cors(withDefaults())
            .csrf(csrf -> csrf
                .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
                .csrfTokenRequestHandler(new SpaCsrfTokenRequestHandler()))
            .addFilterAfter(new SpaWebFilter(), BasicAuthenticationFilter.class)
            .addFilterAfter(new CookieCsrfFilter(), BasicAuthenticationFilter.class)
            .addFilterAfter(new BypassAuthFilter(authBypass, bypassUser), BasicAuthenticationFilter.class)
            .headers(headers -> headers
                .contentSecurityPolicy(csp -> csp
                    .policyDirectives(jHipsterProperties.getSecurity().getContentSecurityPolicy()))
                .frameOptions(FrameOptionsConfig::sameOrigin)
                .referrerPolicy(referrer -> referrer
                    .policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers(mvc.pattern("/index.html"), mvc.pattern("/*.js"), mvc.pattern("/*.txt"),
                    mvc.pattern("/*.json"), mvc.pattern("/*.map"), mvc.pattern("/*.css"))
                .permitAll()
                .requestMatchers(mvc.pattern("/*.ico"), mvc.pattern("/*.png"), mvc.pattern("/*.svg"),
                    mvc.pattern("/*.webapp"))
                .permitAll()
                .requestMatchers(mvc.pattern("/assets/**")).permitAll()
                .requestMatchers(mvc.pattern("/content/**")).permitAll()
                .requestMatchers(mvc.pattern("/css/**")).permitAll()
                .requestMatchers(mvc.pattern("/fonts/**")).permitAll()
                .requestMatchers(mvc.pattern("/icons/**")).permitAll()
                .requestMatchers(mvc.pattern("/img/**")).permitAll()
                .requestMatchers(mvc.pattern("/js/**")).permitAll()
                .requestMatchers(mvc.pattern("/insurers/**")).permitAll()
                .requestMatchers(mvc.pattern("/samples/**")).permitAll()
                .requestMatchers(mvc.pattern("/swagger-ui/**")).permitAll()
                .requestMatchers(mvc.pattern("/v3/api-docs/**")).permitAll()
                .requestMatchers(mvc.pattern("/websocket/**")).authenticated()
                .requestMatchers(mvc.pattern("/management/health")).permitAll()
                .requestMatchers(mvc.pattern("/management/health/**")).permitAll()
                .requestMatchers(mvc.pattern("/management/info")).permitAll()
                .requestMatchers(mvc.pattern("/management/prometheus")).permitAll()
                .requestMatchers(mvc.pattern("/management/**")).hasAuthority(ADMIN)
                .requestMatchers(mvc.pattern("/api/authenticate")).permitAll()
                .requestMatchers(mvc.pattern("/api/.well-known/openid-configuration")).permitAll()
                .requestMatchers(mvc.pattern("/api/auth-info")).permitAll()
                .requestMatchers(mvc.pattern("/api/login")).permitAll()
                .requestMatchers(mvc.pattern("/api/logout")).authenticated()
                // Useful for `./bin/mvnw verify`
                .requestMatchers(mvc.pattern("/api/exception-translator-test/**")).authenticated()

                .requestMatchers("/api/account").authenticated()
                .requestMatchers(HttpMethod.GET, "/api/dashboard/**").authenticated()
                .requestMatchers(HttpMethod.DELETE, "/api/comments/*/comment-users/me").authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/bank-transactions/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/buyers/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/cessions/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/comments/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/companies/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/contracts/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/countries/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/credit-limit-requests/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/datastreams/*/download")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/external-credit-insurances/**"))
                .authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/factor-institutions/**")).authenticated()
                .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/invoices/**")).authenticated()

                .requestMatchers(mvc.pattern("/api/bank-transactions/**")).hasAuthority(WRITER)
                .requestMatchers(mvc.pattern("/api/buyers/**")).hasAuthority(WRITER)
                .requestMatchers(mvc.pattern("/api/cessions/**")).hasAuthority(WRITER)
                .requestMatchers(mvc.pattern("/api/comments/**")).hasAuthority(WRITER)
                .requestMatchers(mvc.pattern("/api/credit-limit-requests/**")).hasAuthority(WRITER)
                .requestMatchers(mvc.pattern("/api/invoices/**")).hasAuthority(WRITER)

                .requestMatchers(mvc.pattern("/api/authorities/**")).hasAuthority(CONFIG)
                .requestMatchers(mvc.pattern("/api/category-to-accounts/**")).hasAuthority(CONFIG)
                .requestMatchers(mvc.pattern("/api/companies/**")).hasAuthority(CONFIG)
                .requestMatchers(mvc.pattern("/api/contracts/**")).hasAuthority(CONFIG)
                .requestMatchers(mvc.pattern("/api/datastreams/**")).hasAuthority(CONFIG)
                .requestMatchers(mvc.pattern("/api/users/**")).hasAuthority(CONFIG)

                .requestMatchers(mvc.pattern("/api/external-credit-insurances/**")).hasAuthority(ADMIN)
                .requestMatchers(mvc.pattern("/api/factor-institutions/**")).hasAuthority(ADMIN)
                .requestMatchers(mvc.pattern("/api/organizations/**")).hasAuthority(ADMIN))
            // Redirect to appropriate error page upon failure
            .oauth2Login(oauth2 -> oauth2.failureUrl("/error-401"))
            .oauth2ResourceServer(
                oauth2 -> oauth2.jwt(jwt -> jwt.jwtAuthenticationConverter(authenticationConverter())))
            .oauth2Client(withDefaults());

        // Other security-related configuration
        http.exceptionHandling(
            exception -> exception.authenticationEntryPoint(problemSupport).accessDeniedHandler(problemSupport));

        return http.build();
    }

    @Bean
    MvcRequestMatcher.Builder mvc(HandlerMappingIntrospector introspector) {
        return new MvcRequestMatcher.Builder(introspector);
    }

    Converter<Jwt, AbstractAuthenticationToken> authenticationConverter() {
        JwtAuthenticationConverter jwtAuthenticationConverter = new JwtAuthenticationConverter();
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(new JwtGrantedAuthorityConverter());
        jwtAuthenticationConverter.setPrincipalClaimName(PREFERRED_USERNAME);
        return jwtAuthenticationConverter;
    }

    OAuth2UserService<OidcUserRequest, OidcUser> oidcUserService() {
        final OidcUserService delegate = new OidcUserService();

        return userRequest -> {
            OidcUser oidcUser = delegate.loadUser(userRequest);
            return new DefaultOidcUser(oidcUser.getAuthorities(), oidcUser.getIdToken(), oidcUser.getUserInfo(),
                PREFERRED_USERNAME);
        };
    }

    /**
     * Map authorities from "groups" or "roles" claim in ID Token.
     *
     * @return a {@link GrantedAuthoritiesMapper} that maps groups from
     * the IdP to Spring Security Authorities.
     */
    @Bean
    public GrantedAuthoritiesMapper userAuthoritiesMapper() {
        return authorities -> {
            Set<GrantedAuthority> mappedAuthorities = new HashSet<>();

            authorities.forEach(authority -> {
                // Check for OidcUserAuthority because Spring Security 5.2 returns
                // each scope as a GrantedAuthority, which we don't care about.
                if (authority instanceof OidcUserAuthority oidcUserAuthority) {
                    mappedAuthorities.addAll(
                        SecurityUtils.extractAuthorityFromClaims(oidcUserAuthority.getUserInfo().getClaims()));
                }
            });
            return mappedAuthorities;
        };
    }

    /**
     * Custom CSRF handler to provide BREACH protection for Single-Page Applications
     * (SPA).
     *
     * @see <a href=
     * "https://docs.spring.io/spring-security/reference/servlet/exploits/csrf.html#csrf-integration-javascript-spa">Spring
     * Security Documentation - Integrating with CSRF Protection</a>
     * @see <a href=
     * "https://github.com/jhipster/generator-jhipster/pull/25907">JHipster -
     * use customized SpaCsrfTokenRequestHandler to handle CSRF token</a>
     * @see <a href="https://stackoverflow.com/q/74447118/65681">CSRF protection not
     * working with Spring Security 6</a>
     */
    static final class SpaCsrfTokenRequestHandler implements CsrfTokenRequestHandler {

        private final CsrfTokenRequestHandler plain = new CsrfTokenRequestAttributeHandler();
        private final CsrfTokenRequestHandler xor = new XorCsrfTokenRequestAttributeHandler();

        @Override
        public void handle(HttpServletRequest request, HttpServletResponse response, Supplier<CsrfToken> csrfToken) {
            /*
             * Always use XorCsrfTokenRequestAttributeHandler to provide BREACH protection
             * of
             * the CsrfToken when it is rendered in the response body.
             */
            this.xor.handle(request, response, csrfToken);

            // Render the token value to a cookie by causing the deferred token to be
            // loaded.
            csrfToken.get();
        }

        @Override
        public String resolveCsrfTokenValue(HttpServletRequest request, CsrfToken csrfToken) {
            /*
             * If the request contains a request header, use
             * CsrfTokenRequestAttributeHandler
             * to resolve the CsrfToken. This applies when a single-page application
             * includes
             * the header value automatically, which was obtained via a cookie containing
             * the
             * raw CsrfToken.
             */
            if (StringUtils.hasText(request.getHeader(csrfToken.getHeaderName()))) {
                return this.plain.resolveCsrfTokenValue(request, csrfToken);
            }
            /*
             * In all other cases (e.g. if the request contains a request parameter), use
             * XorCsrfTokenRequestAttributeHandler to resolve the CsrfToken. This applies
             * when a server-side rendered form includes the _csrf request parameter as a
             * hidden input.
             */
            return this.xor.resolveCsrfTokenValue(request, csrfToken);
        }
    }
}
