package com.afakto.service;

import static com.afakto.security.AuthoritiesConstants.ADMIN;
import static com.afakto.security.AuthoritiesConstants.CONFIG;

import com.afakto.domain.User;
import com.afakto.repository.CommentRepository;
import com.afakto.repository.UserRepository;
import com.afakto.security.SecurityUtils;
import com.afakto.service.dto.UserDTO;
import com.afakto.service.mapper.UserMapper;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.MethodArgumentNotValidException;

@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class UserService {

    private final Auth0UserMgmt auth0UserMgmt;
    private final CacheManager cacheManager;
    private final CommentRepository commentRepository;
    private final UserMapper userMapper;
    private final UserRepository userRepository;

    /**
     * Create a user.
     *
     * @param userDTO the entity to save.
     * @return the persisted entity.
     * @throws MethodArgumentNotValidException
     */
    public UserDTO create(UserDTO userDTO) {
        log.debug("Request to create User : {}", userDTO);

        if (!SecurityUtils.hasCurrentUserThisAuthority(ADMIN) && userDTO.getAuthorities().contains(ADMIN)) throw new AccessDeniedException(
            "Only ADMIN can add the ADMIN role"
        );

        auth0UserMgmt.create(userDTO);

        return userMapper.toDto(userRepository.save(userMapper.toEntity(userDTO)));
    }

    /**
     * Update a user.
     *
     * @param userDTO the entity to save.
     * @return the persisted entity.
     */
    public UserDTO update(UserDTO userDTO) {
        log.debug("Request to update User : {}", userDTO);

        User userFromDB = userRepository.findById(userDTO.getId()).orElseThrow();

        if (
            !SecurityUtils.hasCurrentUserThisAuthority(ADMIN) &&
            userDTO.getAuthorities().contains(ADMIN) != userFromDB.getAuthorities().contains(ADMIN)
        ) throw new AccessDeniedException("Only an ADMIN can add or remove the ADMIN role");

        auth0UserMgmt.update(userDTO, userFromDB.getAuthorities());

        clearUserCaches(userDTO.getLogin());

        var toSave = userMapper.toEntity(userDTO);
        if (!SecurityUtils.hasCurrentUserThisAuthority(CONFIG)) {
            // Ensure only CONFIG users can modify user's perimeter
            toSave.setAuthorities(userFromDB.getAuthorities());
            toSave.setCompanies(userFromDB.getCompanies());
        }

        return userMapper.toDto(userRepository.saveAndFlush(toSave));
    }

    /**
     * Get one useder by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<UserDTO> findOne(UUID id) {
        log.debug("Request to get user : {}", id);
        return userRepository.findById(id).map(userMapper::toDto);
    }

    /**
     * Delete the user by id.
     *
     * @param id the id of the entity.
     */
    public void delete(UUID id) {
        log.debug("Request to delete User : {}", id);
        String login = userRepository.findById(id).orElseThrow().getLogin();
        clearUserCaches(login);
        commentRepository.purgeNotificationsFor(id);
        auth0UserMgmt.delete(login);
        userRepository.deleteById(id);
    }

    private void clearUserCaches(String login) {
        log.info("Clearing user cache for {}", login);
        Objects.requireNonNull(cacheManager.getCache(UserRepository.USERS_BY_LOGIN_CACHE)).evict(login);
    }
}
