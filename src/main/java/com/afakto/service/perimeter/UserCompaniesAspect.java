package com.afakto.service.perimeter;

import com.afakto.domain.Company;
import com.afakto.domain.CompanyOwned;
import com.afakto.repository.CompanyRepository;
import com.afakto.security.SecurityUtils;
import com.afakto.service.dto.BaseDTO;
import com.afakto.service.mapper.EntityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;
import java.util.UUID;

/**
 * Aspect to ensure proper company access. Applies to all services that have
 * CompanyOwned entities
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class UserCompaniesAspect {

    private final ApplicationContext context;
    private final CompanyRepository companyRepository;

    /**
     * Check the UUID for company access.
     *
     * @param joinPoint the join point
     * @param uuid      the UUID
     */
    @Before("within(com.afakto.service.*) && args(uuid,..)")
    public void checkUUID(JoinPoint joinPoint, UUID uuid) {
        if (!isTargetCompanyOwned(joinPoint))
            return;

        checkAccess(getRepository(joinPoint).orElseThrow(), uuid);
    }

    /**
     * Check the DTO for company access.
     *
     * @param joinPoint the join point
     * @param dto       the DTO
     */
    @Before("within(com.afakto.service.*) && args(dto,..)")
    public void checkDTO(JoinPoint joinPoint, BaseDTO dto) {
        if (!isTargetCompanyOwned(joinPoint)) return;

        if (dto.getId() == null) {
            // Only check the company for which this entity would be created
            var companyId = getMapper(joinPoint)
                .map(mapper -> mapper.toEntity(dto))
                .map(CompanyOwned::getCompany)
                .map(Company::getId)
                .orElseThrow();
            checkAccess(companyId);
        } else
            getRepository(joinPoint).ifPresent(repository -> checkAccess(repository, dto.getId()));
    }

    /**
     * Get the mapper for related DTO. This is a bit of a silly way, to not require
     * a BaseDTO CompanyOwned mechanism but still easily extract the company id...
     *
     * @param joinPoint the join point
     * @return the mapper if it exists
     */
    private Optional<EntityMapper<BaseDTO, CompanyOwned>> getMapper(JoinPoint joinPoint) {
        return Optional.of(context.getBean(
                StringUtils.uncapitalize(joinPoint.getTarget().getClass().getSimpleName())
                    .replace("Service", "MapperImpl")))
            .filter(EntityMapper.class::isInstance)
            .map(EntityMapper.class::cast);
    }

    private boolean isTargetCompanyOwned(JoinPoint joinPoint) {
        var entityName = joinPoint.getTarget().getClass().getSimpleName().replace("Service", "");
        // Check that this entity class is a CompanyOwned
        try {
            return CompanyOwned.class.isAssignableFrom(Class.forName("com.afakto.domain." + entityName));
        } catch (ClassNotFoundException _ignored) {
        }
        return false;
    }

    /**
     * Get the repository for the entity.
     *
     * @param joinPoint the join point
     * @return the repository if it exists
     */
    private Optional<JpaRepository<CompanyOwned, UUID>> getRepository(JoinPoint joinPoint) {
        return Optional.of(context.getBean(
                StringUtils.uncapitalize(joinPoint.getTarget().getClass().getSimpleName())
                    .replace("Service", "Repository")))
            .filter(JpaRepository.class::isInstance)
            .map(JpaRepository.class::cast);
    }

    /**
     * Get the companies associated with the current user
     *
     * @return the companies
     */
    private boolean userCanAccessCompany(UUID id) {
        var login = SecurityUtils.getCurrentUserLogin().orElseThrow();
        return companyRepository.existsByUserLoginAndCompanyId(login, id);
    }

    /**
     * Check that the company is in the appropriate perimeter
     *
     * @param companyId the company's id to check
     */
    private void checkAccess(UUID companyId) {
        log.info("Checking access to company {}", companyId);
        if (!userCanAccessCompany(companyId))
            throw new AccessDeniedException("User does not have access to this company");
    }

    /**
     * Check that the company is in the appropriate perimeter
     * <p>
     * This check is not strict, it only returns 404 in case of infraction
     *
     * @param repository the repository used to check accesses
     * @param id         the entity's id to check
     */
    private void checkAccess(JpaRepository<CompanyOwned, UUID> repository, UUID id) {
        log.info("Checking access");
        repository.findById(id)
            .map(CompanyOwned::getCompany)
            .filter(company -> userCanAccessCompany(company.getId()))
            .orElseThrow();
    }
}
