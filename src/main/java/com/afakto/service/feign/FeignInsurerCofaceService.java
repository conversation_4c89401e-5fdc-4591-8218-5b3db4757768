package com.afakto.service.feign;

import com.afakto.service.insurer.coface.FeignInsurerCofaceConfig;
import com.afakto.service.insurer.coface.mapping.RequestCofaceCreditLimitModel;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@FeignClient(
    name = "feign-insurer-coface-api",
    url = "${application.insurer.coface.base-url}",
    configuration = FeignInsurerCofaceConfig.class
)
public interface FeignInsurerCofaceService {
    String CREDIT_LIMIT_PRODUCT = "CREDITLIMIT";

    @PostMapping(value = "authentication/v1/token")
    Map<String, String> token(@RequestHeader("x-api-key") String apiKey, @RequestBody Map<String, String> body);

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL")
    JsonNode getProducts();

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL&customerReferences={customerReferences}")
    JsonNode getProductsForBuyer(
        @PathVariable("customerReferences") String customerReferences);

    @GetMapping(value = "tci/product/v2/companies/products?searchType=NORMAL&easyNumbers={easyNumbers}")
    JsonNode getProductsForEasyNumber(
        @PathVariable("easyNumbers") String easyNumbers);

    @GetMapping(value = "tci/product/v2/companies/{easyNumber}/products/deliveries/{deliveryId}")
    JsonNode getDeliveryDecisionForEasyNumber(
        @PathVariable("easyNumber") String easyNumber,
        @PathVariable("deliveryId") String deliveryId
    );

    @GetMapping(value = "tci/company/v2/companies/identifier?countryCode={countryCode}&identifierType={identifierType}&identifierValue={identifierValue}")
    JsonNode getBuyerFromIdentifier(
        @PathVariable("countryCode") String countryCode,
        @PathVariable("identifierType") String identifierType,
        @PathVariable("identifierValue") String identifierValue);

    @GetMapping(value = "tci/company/v2/companies/name?countryCode={countryCode}&companyName={companyName}")
    JsonNode getBuyerFromCompanyName(
        @PathVariable("countryCode") String countryCode,
        @PathVariable("companyName") String companyName);

    @GetMapping(value = "tci/country/v2/countries/identifiers")
    JsonNode getAllIdentifiers();

    @PatchMapping(value = "tci/company/v2/companies/customerreferences")
    JsonNode patchCustomerReference(@RequestBody Map<String, Object> body);

    @PostMapping(value = "tci/product/v2/companies/{easyNumber}/products", consumes = {"application/json", "application/merge-patch+json"})
    JsonNode requestCreditLimitUpdate(
        @PathVariable("easyNumber") String easyNumber,
        @RequestBody RequestCofaceCreditLimitModel body);
}
