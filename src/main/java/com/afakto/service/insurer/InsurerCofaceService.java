package com.afakto.service.insurer;

import com.afakto.domain.Buyer;
import com.afakto.domain.BuyerFromInsurer;
import com.afakto.domain.Contract;
import com.afakto.repository.BuyerFromInsurerRepository;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.SearchRequest;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.feign.InsurerSearchResult;
import com.afakto.service.insurer.coface.CofaceRequestContextScope;
import com.afakto.service.insurer.coface.Consult;
import com.afakto.service.insurer.coface.Request;
import com.afakto.service.insurer.coface.Search;
import com.afakto.service.insurer.coface.mapping.CofaceNumberTypeMapping;
import com.afakto.service.insurer.coface.mapping.ConsultCofaceModel;
import com.afakto.service.insurer.coface.mapping.JsonWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * This service is used to import data from Insurer's COFACE API.
 * <p></p>
 * API documentation:
 * <a href="https://developers.coface.com/docs-technical?sw=CofaServe%20-%20API%20Product.yaml&swl=API%20CofaServe%20-%20Product#tag/Product">...</a>
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class InsurerCofaceService {
    private static final String INSURER_NAME = "coface";
    private final BuyerRepository buyerRepository;
    private final BuyerFromInsurerRepository buyerFromInsurerRepository;
    private final FeignInsurerCofaceService feignInsurerService;
    private final Search search;
    private final Request request;
    private final Consult consult;
    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final ObjectMapper objectMapper;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;

    public void requestCreditLimit(Buyer buyer, BigDecimal amount) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        String insurerCode = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .map(BuyerFromInsurer::getInsurerCode)
            .orElseGet(() -> request.getInsurerCode(header, buyer));

        JsonNode response = request.sendRequestCreditLimit(buyer, header, insurerCode, amount);

        BuyerFromInsurer buyerFromInsurer = Optional.ofNullable(buyer.getBuyerFromInsurer())
            .orElseGet(BuyerFromInsurer::new);

        buyerFromInsurer.setInsurerName(INSURER_NAME);
        buyerFromInsurer.setInsurerCode(response != null
            ? response.get("easyNumber").asText()
            : insurerCode);

        buyerFromInsurer = buyerFromInsurerRepository.save(buyerFromInsurer.setBuyer(buyer));
        buyerRepository.save(buyer.setBuyerFromInsurer(buyerFromInsurer));
    }

    public int updateCreditLimits(Contract contract) {
        JsonWrapper<ConsultCofaceModel> cover;

        try (var ignored = new CofaceRequestContextScope(feignInsurerCofaceHelper.from(contract))) {
            cover = JsonWrapper.fromJson(feignInsurerService.getProducts(), ConsultCofaceModel.class, objectMapper);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return 0;
        }

        consult.alreadyImported = new HashMap<>();

        var toSave = IntStream.range(0, cover.model().companies().size())
            .mapToObj(i -> {
                ConsultCofaceModel.Company company = cover.model().companies().get(i);
                JsonNode rawCompany = cover.raw().get("companies").get(i);

                var buyer = consult.findBuyer(contract.getCompany(), company);
                if (buyer == null)
                    return null;
                return consult.setupBuyerFromInsurer(buyer, company, rawCompany);
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        consult.alreadyImported = null;
        log.warn("Saving {} buyers", toSave.size());
        return buyerFromInsurerRepository.saveAll(toSave).size();
    }

    public Buyer updateCreditLimit(Buyer buyer) {
        FeignInsurerCofaceHeader header = feignInsurerCofaceHelper.from(buyer);

        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        JsonWrapper<ConsultCofaceModel.Company> cover = consult.getCover(header, easyNumber, buyer.getCode());

        if (cover == null)
            throw new IllegalArgumentException("Couldn't find cover for buyer");

        if (!buyer.getCode().equals(cover.model().customerReferenceValue()))
            consult.patchCustomerReference(header, easyNumber, buyer.getCode());
        var toSave = consult.setupBuyerFromInsurer(buyer, cover.model(), cover.raw());
        if (toSave == null)
            return buyer;
        return buyer.setBuyerFromInsurer(buyerFromInsurerRepository.save(toSave));
    }

    public BuyerFromInsurer updateInsurerDecision(Buyer buyer, String deliveryId) {
        log.info("InsurerDecision update");

        JsonNode insurerDecision;
        var easyNumber = buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode();
        try (var ignored = new CofaceRequestContextScope(feignInsurerCofaceHelper.from(buyer))) {
            insurerDecision = feignInsurerService.getDeliveryDecisionForEasyNumber(easyNumber, deliveryId);
        }

        if (insurerDecision == null)
            throw new IllegalArgumentException("Couldn't find insurer decision for credit limit code " + deliveryId);

        buyer.getBuyerFromInsurer().setRawDecision(objectMapper.convertValue(insurerDecision, new TypeReference<>() {
        }));
        return buyerFromInsurerRepository.save(buyer.getBuyerFromInsurer());
    }

    public InsurerSearchResult searchBuyer(SearchRequest searchRequest) {
        log.debug("COFACE credit insurance policy found, now searching");
        List<BuyerDTO> buyers = new ArrayList<>();
        String preferredIdentifier = search.getCofacePreferredIdentifier(searchRequest);

        ConsultCofaceModel node;
        try {
            node = search.search(searchRequest, preferredIdentifier);
        } catch (FeignException exception) {
            return InsurerSearchResult.failure(search.getCofaceErrorCode(exception.getMessage()));
        }

        for (ConsultCofaceModel.Company cofaceCompany : node.companies()) {
            BuyerDTO filledBuyer = search.mapToBuyerData(cofaceCompany, preferredIdentifier);
            if (filledBuyer.getNumberType() != null)
                buyers.add(filledBuyer);
        }

        return InsurerSearchResult.success(buyers);
    }

}
