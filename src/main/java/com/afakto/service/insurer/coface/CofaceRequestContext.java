package com.afakto.service.insurer.coface;

import com.afakto.service.feign.FeignInsurerCofaceHeader;

public class CofaceRequestContext {
    private static final ThreadLocal<FeignInsurerCofaceHeader> context = new ThreadLocal<>();

    public static FeignInsurerCofaceHeader getHeader() {
        return context.get();
    }

    public static void setHeader(FeignInsurerCofaceHeader header) {
        context.set(header);
    }

    public static void clear() {
        context.remove();
    }
}

