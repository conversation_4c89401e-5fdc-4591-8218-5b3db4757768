package com.afakto.service.insurer.coface;

import com.afakto.service.feign.FeignInsurerCofaceHeader;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignInsurerCofaceConfig {
    @Bean
    public RequestInterceptor cofaceHeaderInterceptor() {
        return requestTemplate -> {
            FeignInsurerCofaceHeader header = CofaceRequestContext.getHeader();
            if (header == null) return;
            requestTemplate.header("Authorization", header.idToken());
            requestTemplate.header("X-COF-Policy-Number", header.policyId());
            requestTemplate.header("x-api-key", header.apiKey());
        };
    }
}
