package com.afakto.service.insurer.coface;

import com.afakto.domain.Buyer;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.mapping.CofaceNumberTypeMapping;
import com.afakto.service.insurer.coface.mapping.RequestCofaceCreditLimitModel;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.stream.StreamSupport;

import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class Request {

    private static final String UNKNOWN_EASYNUMBER = "unknown";
    private static final String CREATE_ACTION_CODE = "CREA";
    private static final String UPDATE_ACTION_CODE = "UPDT";
    private static final String CHANGE_ACTION_CODE = "CHANGE";
    private final FeignInsurerCofaceService feignInsurerService;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;

    public String getInsurerCode(FeignInsurerCofaceHeader header, Buyer buyer) {
        JsonNode buyerFromIdentifier;
        try (var ignored = new CofaceRequestContextScope(header)) {
            buyerFromIdentifier = feignInsurerService.getBuyerFromIdentifier(
                mapTo3LetterCode(buyer.getAddress().getCountry()),
                getNumberType(buyer), buyer.getNumber());
        }
        if (buyerFromIdentifier == null)
            return null;
        if (buyerFromIdentifier.has("companies") && !buyerFromIdentifier.get("companies").isEmpty())
            return buyerFromIdentifier.get("companies").get(0).get("easyNumber").asText();
        return null;
    }

    public JsonNode sendRequestCreditLimit(Buyer buyer, FeignInsurerCofaceHeader header, String insurerCode, BigDecimal amount) {
        final RequestCofaceCreditLimitModel requestData = prepareRequestData(buyer, header, insurerCode);

        if (requestData == null)
            return null;

        if (insurerCode == null)
            insurerCode = UNKNOWN_EASYNUMBER;

        buildRequestPayload(buyer, amount, requestData);

        try (var ignored = new CofaceRequestContextScope(header)) {
            return feignInsurerService.requestCreditLimitUpdate(insurerCode, requestData);
        }
    }

    private RequestCofaceCreditLimitModel prepareRequestData(Buyer buyer, FeignInsurerCofaceHeader header, String insurerCode) {
        if (UNKNOWN_EASYNUMBER.equals(insurerCode))
            return createUnknownBuyerCreditLimitRequest(buyer);
        return getDeliveryType(header, insurerCode);
    }

    private void buildRequestPayload(Buyer buyer, BigDecimal amount, RequestCofaceCreditLimitModel requestData) {
        requestData.productCode = FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT;
        requestData.orderDetails = new RequestCofaceCreditLimitModel.OrderDetails(amount, buyer.getCurrency());
    }

    private String getNumberType(Buyer buyer) {
        String countryCode = mapTo3LetterCode(buyer.getAddress().getCountry());
        return cofaceNumberTypeMapping.getIntoCofaceNumberType()
            .get(countryCode)
            .getOrDefault(buyer.getNumberType(), buyer.getNumberType().toString());
    }

    private RequestCofaceCreditLimitModel getDeliveryType(FeignInsurerCofaceHeader header, String insurerCode) {
        JsonNode productsResponse;
        try (var ignored = new CofaceRequestContextScope(header)) {
            productsResponse = feignInsurerService.getProductsForEasyNumber(insurerCode);
        }
        if (productsResponse == null || !productsResponse.has("companies") || productsResponse.get("companies").isEmpty()) {
            log.warn("No products or companies found for easy number: {}", insurerCode);
            return createCreditLimitRequest(CREATE_ACTION_CODE, null, new RequestCofaceCreditLimitModel());
        }

        JsonNode product = findFirstProduct(productsResponse);

        if (product == null || !product.has("deliveryId")) {
            log.warn("No deliveryId found for easy number: {}", insurerCode);
            return createCreditLimitRequest(CREATE_ACTION_CODE, null, new RequestCofaceCreditLimitModel());
        }

        if (product.get("deliveryId").isNull()) {
            log.warn("DeliveryId is null for easy number: {}", insurerCode); // todo send message delivery is still pending
            return null;
        }

        String actionCode = FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT.equals(product.get("productCode").asText())
            ? UPDATE_ACTION_CODE
            : CHANGE_ACTION_CODE;
        return createCreditLimitRequest(actionCode, product.get("deliveryId").asText(), new RequestCofaceCreditLimitModel());
    }

    private JsonNode findFirstProduct(JsonNode products) {
        return StreamSupport.stream(products.get("companies").get(0).get("products").spliterator(), false)
            .filter(item -> item.has("productCode"))
            .findFirst()
            .orElse(null);
    }

    private RequestCofaceCreditLimitModel createCreditLimitRequest(String actionCode, String deliveryId, RequestCofaceCreditLimitModel requestData) {
        requestData.deliveryId = deliveryId;
        requestData.actionCode = actionCode;
        return requestData;
    }

    private RequestCofaceCreditLimitModel createUnknownBuyerCreditLimitRequest(Buyer buyer) {
        final RequestCofaceCreditLimitModel requestData = new RequestCofaceCreditLimitModel();

        requestData.actionCode = CREATE_ACTION_CODE;
        requestData.unknownDebtor = new RequestCofaceCreditLimitModel.UnknownDebtor(buyer.getName(),
            mapTo3LetterCode(buyer.getAddress().getCountry()),
            new RequestCofaceCreditLimitModel.UnknownDebtor.Address(buyer.getAddress().getStreetNumber() + buyer.getAddress().getStreetName(),
                buyer.getAddress().getPostalCode(),
                buyer.getAddress().getCity()));

        return requestData;
    }


}
