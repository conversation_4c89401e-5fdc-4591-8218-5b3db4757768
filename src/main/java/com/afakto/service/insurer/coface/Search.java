package com.afakto.service.insurer.coface;

import com.afakto.domain.enumeration.NumberType;
import com.afakto.service.dto.AddressDTO;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.SearchRequest;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.mapping.CofaceNumberTypeMapping;
import com.afakto.service.insurer.coface.mapping.ConsultCofaceModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.afakto.batch.Utils.iso3ToIso2;
import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class Search {

    private final FeignInsurerCofaceService feignInsurerService;
    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;
    private final ObjectMapper objectMapper;

    public ConsultCofaceModel search(SearchRequest searchRequest, String cofaceIdentifier) throws FeignException {
        String countryCode = mapTo3LetterCode(searchRequest.getCountry());

        JsonNode result;
        try (var ignored = new CofaceRequestContextScope(feignInsurerCofaceHelper.fromTest())) {
            if (searchRequest.getInsurerCode() != null)
                result = feignInsurerService.getProductsForEasyNumber(searchRequest.getInsurerCode());
            else if (searchRequest.getNumber() != null && searchRequest.getNumber().length() > 3 && cofaceIdentifier != null) {
                if (countryCode.equalsIgnoreCase("ESP"))
                    if (!searchRequest.getNumber().startsWith("ES"))
                        searchRequest.setNumber("ES" + searchRequest.getNumber());
                result = feignInsurerService.getBuyerFromIdentifier(countryCode, cofaceIdentifier, searchRequest.getNumber());
            } else
                result = feignInsurerService.getBuyerFromCompanyName(countryCode, searchRequest.getName());
        }

        try {
            return objectMapper.treeToValue(result, ConsultCofaceModel.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public BuyerDTO mapToBuyerData(ConsultCofaceModel.Company cofaceCompany, String preferredIdentifier) {
        BuyerDTO buyer = new BuyerDTO();
        AddressDTO addressDTO = getAddress(cofaceCompany.companyDetails().address());
        updateIdentifier(cofaceCompany, buyer, preferredIdentifier);
        return buyer.setAddress(addressDTO).setName(cofaceCompany.companyDetails().name());
    }

    private void updateIdentifier(ConsultCofaceModel.Company cofaceCompany, BuyerDTO buyer, String preferredIdentifier) {
        List<ConsultCofaceModel.Identifier> identifiers = getAllIdentifiers(cofaceCompany);

        Optional<ConsultCofaceModel.Identifier> selectedIdentifier =
            findIdentifierStartingWithVAT(identifiers)
                .or(() -> findIdentifierMatchingPreferred(identifiers, preferredIdentifier))
                .or(() -> getMainIdentifier(cofaceCompany))
                .or(() -> findAnySecondaryIdentifier(cofaceCompany));

        selectedIdentifier.ifPresent(id -> setBuyerIdentifier(buyer, id));
    }

    private List<ConsultCofaceModel.Identifier> getAllIdentifiers(ConsultCofaceModel.Company company) {
        List<ConsultCofaceModel.Identifier> all = new ArrayList<>();
        if (company.mainIdentifier() != null)
            all.add(company.mainIdentifier());
        if (company.secondaryIdentifiers() != null)
            all.addAll(company.secondaryIdentifiers());
        return all;
    }

    private Optional<ConsultCofaceModel.Identifier> findIdentifierStartingWithVAT(List<ConsultCofaceModel.Identifier> identifiers) {
        return identifiers.stream()
            .filter(id -> id.type() != null && id.type().startsWith("VAT_"))
            .findFirst();
    }

    private Optional<ConsultCofaceModel.Identifier> findIdentifierMatchingPreferred(List<ConsultCofaceModel.Identifier> identifiers, String preferred) {
        if (preferred == null) return Optional.empty();
        return identifiers.stream()
            .filter(id -> preferred.equals(id.type()))
            .findFirst();
    }

    private Optional<ConsultCofaceModel.Identifier> getMainIdentifier(ConsultCofaceModel.Company company) {
        return Optional.ofNullable(company.mainIdentifier());
    }

    private Optional<ConsultCofaceModel.Identifier> findAnySecondaryIdentifier(ConsultCofaceModel.Company company) {
        if (company.secondaryIdentifiers() == null) return Optional.empty();
        return company.secondaryIdentifiers().stream().findFirst();
    }

    private void setBuyerIdentifier(BuyerDTO buyer, ConsultCofaceModel.Identifier cofaceIdentifier) {
        NumberType numberType = cofaceNumberTypeMapping.getFromCofaceNumberType().get(cofaceIdentifier.type());
        if (numberType == null)
            return;
        buyer.setNumber(cofaceIdentifier.value()).setNumberType(numberType);
    }

    private AddressDTO getAddress(ConsultCofaceModel.CompanyDetails.Address address) {
        return new AddressDTO()
            .setCity(address.city())
            .setCountry(iso3ToIso2(address.countryCode()).toLowerCase())
            .setPostalCode(address.postalCode())
            .setStreetName(address.streets() != null && !address.streets().isEmpty() ? address.streets().getFirst() : null);
    }

    public String getCofaceErrorCode(String message) {
        int startIndex = message.indexOf("[{");
        int endIndex = message.lastIndexOf("}]") + "}]".length();

        if (startIndex == -1 || endIndex <= startIndex)
            throw new RuntimeException(message);

        message = message.substring(startIndex, endIndex);
        try {
            JsonNode node = objectMapper.readTree(message).get(0);
            String code = node.get("code").asText();

            if (code.equals("Client403-SOR_BR230236"))
                return "COUNTRY_NOT_ALLOWED";
            if (code.equals("Client412"))
                return "VAT_INVALID";
            return message;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Couldn't parse error message: " + message);
        }
    }

    public String getCofacePreferredIdentifier(SearchRequest searchRequest) {
        String countryCode = mapTo3LetterCode(searchRequest.getCountry());
        if (searchRequest.getNumberType() != null && cofaceNumberTypeMapping.getIntoCofaceNumberType().getOrDefault(countryCode, new HashMap<>()).containsKey(searchRequest.getNumberType()))
            return cofaceNumberTypeMapping.getIntoCofaceNumberType().getOrDefault(countryCode, new HashMap<>()).getOrDefault(searchRequest.getNumberType(), null);
        return null;
    }

}
