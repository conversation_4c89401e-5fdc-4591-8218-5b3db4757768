package com.afakto.service.insurer.coface;

import com.afakto.domain.*;
import com.afakto.domain.enumeration.CreditLimitRequestStatus;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.CreditLimitRequestRepository;
import com.afakto.service.CommentService;
import com.afakto.service.feign.FeignInsurerCofaceHeader;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.mapping.ConsultCofaceModel;
import com.afakto.service.insurer.coface.mapping.JsonWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
@Slf4j
public class Consult {

    private final BuyerRepository buyerRepository;
    private final FeignInsurerCofaceService feignInsurerService;
    private final CreditLimitRequestRepository creditLimitRequestRepository;
    private final CommentService commentService;
    private final ObjectMapper objectMapper;
    public Map<String, BuyerFromInsurer> alreadyImported;

    public JsonWrapper<ConsultCofaceModel.Company> getCover(FeignInsurerCofaceHeader header, String easyNumber, String customerReference) {
        JsonNode rawCover = null;
        JsonWrapper<ConsultCofaceModel.Company> cover = null;
        JsonNode productFromCustomerReference = null;

        try (var ignored = new CofaceRequestContextScope(header)) {
            productFromCustomerReference = feignInsurerService.getProductsForBuyer(customerReference);
        } catch (FeignException e) {
            log.error(e.getMessage());
        }

        if (productFromCustomerReference != null && productFromCustomerReference.has("companies")
            && !productFromCustomerReference.get("companies").isEmpty())
            rawCover = productFromCustomerReference.get("companies").get(0);
        else if (easyNumber != null)
            try (var ignored = new CofaceRequestContextScope(header)) {
                rawCover = feignInsurerService.getProductsForEasyNumber(easyNumber)
                    .get("companies").get(0);
            }

        try {
            cover = JsonWrapper.fromJson(rawCover, ConsultCofaceModel.Company.class, objectMapper);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
        }

        return cover;
    }

    private void updateProduct(CreditLimitRequest creditLimitRequest, ConsultCofaceModel.Product product) {
        log.debug("updating creditLimitRequest: [id:{}, orderCode:{}, requestedAmount:{}, amount:{}] with COFACE product: {}", creditLimitRequest.getId(), creditLimitRequest.getOrderCode(), creditLimitRequest.getRequestedAmount(), creditLimitRequest.getAmount(), product.toString());
        switch (product.deliveryStatus().toUpperCase()) {
            case "DECIDED_FULL":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.ACCEPTED)
                    .setAmount(product.position().amount().value())
                    .setOrderCode(product.deliveryId());
                break;
            case "DECIDED_PARTIAL":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.PARTIALLY_ACCEPTED)
                    .setAmount(product.position().amount().value())
                    .setOrderCode(product.deliveryId());
                break;
            case "DECIDED_REFUSED":
                creditLimitRequest
                    .setStatus(CreditLimitRequestStatus.REJECTED)
                    .setAmount(BigDecimal.ZERO)
                    .setOrderCode(product.deliveryId());
                break;
            default:
                log.warn("Unknown deliveryStatus: {}", product.deliveryStatus().toUpperCase());
                break;
        }

        log.info("Updating credit limit request: [{}]{}:{} | {}",
            creditLimitRequest.getStatus(),
            creditLimitRequest.getAmount(),
            creditLimitRequest.getRequestedAmount(),
            creditLimitRequest.getCurrency());
        creditLimitRequestRepository.save(creditLimitRequest);
    }

    public BuyerFromInsurer setupBuyerFromInsurer(Buyer buyer, ConsultCofaceModel.Company cofaceNode, JsonNode rawCover) {
        if (buyer == null || cofaceNode == null) {
            log.warn("Buyer or cover is null: {} {}", buyer, cofaceNode);
            return null;
        }
        List<ConsultCofaceModel.Product> products = cofaceNode.products().stream()
            .filter(item -> item.productCode() != null)
            .filter(item -> FeignInsurerCofaceService.CREDIT_LIMIT_PRODUCT.equals(item.productCode())).toList();

        var acceptedProduct = products.stream()
            .filter(item -> item.deliveryId() != null)
            .findFirst()
            .orElse(null);

        log.info("Importing credit limit for buyer code '{}', insurer code '{}', from cover: {}",
            buyer.getCode(),
            buyer.getBuyerFromInsurer() == null ? null : buyer.getBuyerFromInsurer().getInsurerCode(),
            acceptedProduct);
        if (acceptedProduct == null || acceptedProduct.position() == null || acceptedProduct.position().amount() == null || acceptedProduct.position().amount().value() == null)
            return null;

        // Set the credit limit
        if (buyer.getCreditLimit() == null) {
            buyer.setCreditLimit(new CreditLimit());
        }

        var isObsolete = false;
        if (acceptedProduct.endDate() != null) {
            var endDate = OffsetDateTime.parse(
                acceptedProduct.endDate(),
                DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            isObsolete = OffsetDateTime.now().isAfter(endDate);
            log.info("Buyer {} is obsolete: {} because of end date {}",
                buyer.getName(),
                isObsolete,
                acceptedProduct.endDate());
        }

        // Set the related insurer data, to display some kind of report
        var buyerFromInsurer = buyer.getBuyerFromInsurer();
        if (buyerFromInsurer == null)
            buyerFromInsurer = new BuyerFromInsurer().setBuyer(buyer);

        if (alreadyImported != null) {
            if (alreadyImported.containsKey(buyer.getCode())) {
                var msg = "[Coface Sync] Insurer has duplicate data for " + buyer.getCode()
                    + " / " + buyer.getBuyerFromInsurer().getInsurerCode()
                    + " with names:"
                    + "\n1: " + alreadyImported.get(buyer.getCode()).getRaw().get("internationalName")
                    + "\n2: " + cofaceNode.internationalName();
                log.warn("{}. FIRST: {}; SECOND: {}", msg, alreadyImported.get(buyer.getCode()).getRaw(), cofaceNode);
                commentService.notify(buyer, msg);
                return null;
            } else {
                alreadyImported.put(buyer.getCode(), buyerFromInsurer);
            }
        }

        var position = acceptedProduct.position().amount();
        buyer.getCreditLimit()
            .setAmount(isObsolete ? BigDecimal.ZERO : position.value())
            .setCurrency(position.currency());
        buyerRepository.save(buyer);

        var coverMap = objectMapper.convertValue(rawCover, new TypeReference<Map<String, Object>>() {
        });

        // update the product history if the most recent COFACE product corresponds to the current product
        if (products.getFirst() == acceptedProduct) {
            creditLimitRequestRepository.getByBuyerId(buyer.getId())
                .stream()
                .reduce((first, second) -> second)
                .ifPresent(lastRequest -> updateProduct(lastRequest, acceptedProduct));
        }

        return buyerFromInsurer
            .setInsurerCode(cofaceNode.easyNumber())
            .setInsurerName("coface")
            .setRaw(coverMap);
    }

    public void patchCustomerReference(FeignInsurerCofaceHeader header, String easyNumber, String customerReference) {
        log.debug("patching customer reference value for buyer with new reference: {}", customerReference);
        Map<String, Object> body = Map.of("easyNumber", easyNumber, "customerReference", customerReference);
        try (var ignored = new CofaceRequestContextScope(header)) {
            feignInsurerService.patchCustomerReference(body);
        }
    }

    public Buyer findBuyer(Company company, ConsultCofaceModel.Company cofaceCompany) {
        if (cofaceCompany.customerReferenceValue() != null)
            return buyerRepository.findOneByCompanyAndCode(company, cofaceCompany.customerReferenceValue()).orElse(null);

        if (cofaceCompany.easyNumber() != null)
            return buyerRepository.findOneByCompanyAndBuyerFromInsurerInsurerCode(company, cofaceCompany.easyNumber()).orElse(null);

        log.warn("No buyer found for cover: {}", cofaceCompany);
        return null;
    }
}
