package com.afakto.web.rest;

import com.afakto.config.PublicEndpoint;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.*;
import com.afakto.service.criteria.BuyerCriteria;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.ContractDTO;
import com.afakto.service.dto.DatastreamDTO;
import com.afakto.service.dto.SearchRequest;
import com.afakto.service.feign.InsurerSearchResult;
import com.afakto.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * Represents a debtor owing the invoice amount in the factoring process.
 * Identified by:
 * - Company they belong to
 * - Code (unique identifier within the company)
 * - Number type (e.g., VAT, SIREN, DUNS)
 * - Number (the actual identifier value)
 * Can have:
 * - Address information
 * - Contact details
 * - Payment terms
 * - Credit limits (from factors or insurers)
 * - External data from factors or insurers
 */
@RequestMapping("/api/buyers")
@RequiredArgsConstructor
@RestController
@Slf4j
public class BuyerResource {
    private static final String ENTITY_NAME = "buyer";

    private final BuyerFileUploadService buyerFileUploadService;
    private final BuyerQueryService buyerQueryService;
    private final BuyerRepository buyerRepository;
    private final BuyerService buyerService;
    private final FileService fileService;
    private final DatastreamService datastreamService;

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    @Value("file:${application.filesystem}/archive")
    private Resource archiveDir;

    /**
     * Create a new buyer.
     *
     * @param buyerDTO buyer data to create
     * @return the created buyer
     * @throws URISyntaxException if URI syntax is incorrect
     */
    @PublicEndpoint
    @PostMapping
    public ResponseEntity<BuyerDTO> createBuyer(@Valid @RequestBody BuyerDTO buyerDTO) throws URISyntaxException {
        log.debug("REST request to save Buyer : {}", buyerDTO);
        if (buyerDTO.getId() != null) {
            throw new BadRequestAlertException("A new buyer cannot already have an ID", ENTITY_NAME, "idexists");
        }
        BuyerDTO result = buyerService.save(buyerDTO);
        return ResponseEntity
            .created(new URI("/api/buyers/" + result.getId()))
            .headers(
                HeaderUtil.createEntityCreationAlert(
                    applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * Upload and process CSV files with buyer data.
     *
     * @param files CSV files with buyer data to import
     * @return processing results
     * @throws IOException if file processing fails
     */
    @PostMapping("upload")
    public ResponseEntity<Collection<DatastreamDTO>> uploadBuyerFile(
        @RequestParam("files") MultipartFile[] files) throws IOException {
        var results = new ArrayList<DatastreamDTO>();
        for (var file : files) {
            log.debug("REST request to import Buyers from file {}", file.getOriginalFilename());
            var datastream = buyerFileUploadService.processFile(file);

            fileService.archiveResource(file.getResource(), datastreamService.reconstructPath("archive", datastream));

            results.add(datastream);
        }
        return ResponseEntity.ok().body(results);
    }

    /**
     * Update an existing buyer.
     *
     * @param id       buyer ID
     * @param buyerDTO buyer data to update
     * @return the updated buyer
     */
    @PublicEndpoint
    @PutMapping("{id}")
    public ResponseEntity<BuyerDTO> updateBuyer(
        @PathVariable(value = "id", required = false) final UUID id,
        @Valid @RequestBody BuyerDTO buyerDTO) {
        log.debug("REST request to update Buyer : {}, {}", id, buyerDTO);
        if (buyerDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, buyerDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!buyerRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        BuyerDTO result = buyerService.save(buyerDTO);
        return ResponseEntity
            .ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME,
                buyerDTO.getId().toString()))
            .body(result);
    }

    /**
     * Partially update a buyer (only provided fields).
     *
     * @param id       buyer ID
     * @param buyerDTO buyer data with fields to update
     * @return the updated buyer
     */
    @PublicEndpoint
    @PatchMapping(value = "{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<BuyerDTO> partialUpdateBuyer(
        @PathVariable(value = "id", required = false) final UUID id,
        @NotNull @RequestBody BuyerDTO buyerDTO) {
        log.debug("REST request to partial update Buyer partially : {}, {}", id, buyerDTO);
        if (buyerDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, buyerDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!buyerRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<BuyerDTO> result = buyerService.partialUpdate(buyerDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, buyerDTO.getId().toString()));
    }

    /**
     * Get all buyers with optional filtering.
     *
     * @param criteria filter criteria
     * @param pageable pagination information
     * @return list of buyers
     */
    @PublicEndpoint
    @GetMapping
    public ResponseEntity<List<BuyerDTO>> getAllBuyers(
        BuyerCriteria criteria,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        log.debug("REST request to get a page of Buyers");
        Page<BuyerDTO> page = buyerQueryService.findByCriteria(criteria, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * Count buyers matching the criteria.
     *
     * @param criteria filter criteria
     * @return count of matching buyers
     */
    @PublicEndpoint
    @GetMapping("count")
    public ResponseEntity<Long> countBuyers(BuyerCriteria criteria) {
        log.debug("REST request to count Buyers by criteria: {}", criteria);
        return ResponseEntity.ok().body(buyerQueryService.countByCriteria(criteria));
    }

    /**
     * Get a specific buyer by ID.
     *
     * @param id buyer ID
     * @return the buyer or 404 if not found
     */
    @PublicEndpoint
    @GetMapping("{id}")
    public ResponseEntity<BuyerDTO> getBuyer(@PathVariable UUID id) {
        log.debug("REST request to get Buyer : {}", id);
        Optional<BuyerDTO> buyerDTO = buyerService.findOne(id);
        return ResponseUtil.wrapOrNotFound(buyerDTO);
    }

    /**
     * Get the contract associated with a buyer.
     *
     * @param id buyer identification information
     * @return the associated contract or 404 if none found
     */
    @PublicEndpoint
    @GetMapping("{id}/contract")
    public ResponseEntity<ContractDTO> getContract(@PathVariable UUID id) {
        log.debug("REST request to get Buyer contract : {}", id);
        Optional<ContractDTO> contractDTO = buyerService.findOne(id).flatMap(buyerService::getContract);
        return ResponseUtil.wrapOrNotFound(contractDTO);
    }

    @PostMapping("search")
    public ResponseEntity<InsurerSearchResult> searchBuyer(@RequestBody SearchRequest searchRequest) {
        log.debug(
            "REST request to search with [{}]",
            searchRequest.toString());

        InsurerSearchResult result = buyerService.searchBuyer(searchRequest);
        if (result.isSuccess())
            return ResponseEntity.ok().body(result);
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * Delete a buyer.
     *
     * @param id buyer ID to delete
     * @return no content on success
     */
    @PublicEndpoint
    @DeleteMapping("{id}")
    public ResponseEntity<Void> deleteBuyer(@PathVariable UUID id) {
        log.debug("REST request to delete Buyer : {}", id);
        buyerService.delete(id);
        return ResponseEntity
            .noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * Update credit limit for a buyer from external sources.
     *
     * @param id buyer ID
     * @return updated buyer with new credit limit
     */
    @PostMapping("{id}/updateCreditLimit")
    public ResponseEntity<BuyerDTO> updateCreditLimit(@PathVariable UUID id) {
        log.debug("REST request to update credit limits for buyer: {}", id);
        var updated = buyerService.updateCreditLimit(id);
        return ResponseEntity.ok(updated);
    }
}
