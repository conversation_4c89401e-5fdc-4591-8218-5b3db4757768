package com.afakto.web.rest;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import com.afakto.domain.CreditLimitRequest;
import com.afakto.repository.BuyerRepository;
import com.afakto.service.CreditLimitRequestService;
import com.afakto.service.dto.BuyerFromInsurerDTO;
import com.afakto.service.dto.CreditLimitRequestDTO;
import com.afakto.web.rest.errors.BadRequestAlertException;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing
 * {@link CreditLimitRequest}.
 */
@RequestMapping("/api")
@RequiredArgsConstructor
@RestController
@Slf4j
public class CreditLimitRequestResource {
    private static final String ENTITY_NAME = "creditLimitRequest";

    private final BuyerRepository buyerRepository;
    private final CreditLimitRequestService creditLimitRequestService;

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * {@code POST /buyers/{id}/credit-limit-requests} : Create a new
     * creditLimitRequest.
     *
     * @param creditLimitRequestDTO the creditLimitRequestDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with
     * body the new creditLimitRequestDTO, or with status
     * {@code 400 (Bad Request)} if the creditLimitRequest has already an
     * ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("credit-limit-requests")
    public ResponseEntity<CreditLimitRequestDTO> createCreditLimitRequest(
        @Valid @RequestBody CreditLimitRequestDTO creditLimitRequestDTO) throws URISyntaxException {
        log.debug("REST request to save CreditLimitRequest : {}", creditLimitRequestDTO);

        if (!buyerRepository.existsById(creditLimitRequestDTO.getBuyer().getId())) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        CreditLimitRequestDTO result = creditLimitRequestService.save(creditLimitRequestDTO);
        return ResponseEntity
            .created(new URI(
                MessageFormat.format("/api/buyers/{0}/credit-limit-requests/{1}",
                    creditLimitRequestDTO.getBuyer().getId(),
                    result.getId())))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME,
                result.getId().toString()))
            .body(result);
    }

    /**
     * Get all credit limit requests for a buyer.
     *
     * @param buyerId  buyer ID
     * @param pageable pagination information
     * @return list of credit limit requests
     */
    @GetMapping("/buyers/{buyerId}/credit-limit-requests")
    public ResponseEntity<List<CreditLimitRequestDTO>> getCreditLimitRequests(
        @PathVariable(value = "buyerId", required = true) final UUID buyerId,
        @ParameterObject Pageable pageable) {
        log.debug("REST request to get all CreditLimitRequests");
        if (buyerId == null) {
            throw new BadRequestAlertException("Invalid buyerId", ENTITY_NAME, "idnull");
        }
        Page<CreditLimitRequestDTO> page = creditLimitRequestService.findByBuyer(buyerId, pageable);
        HttpHeaders headers = PaginationUtil
            .generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * Get all credit limit requests.
     *
     * @param pageable pagination information
     * @return list of credit limit requests
     */
    @GetMapping("/credit-limit-requests")
    public ResponseEntity<List<CreditLimitRequestDTO>> getAllCreditLimitRequests(
        @ParameterObject Pageable pageable) {
        log.debug("REST request to get all CreditLimitRequests");

        Page<CreditLimitRequestDTO> page = creditLimitRequestService.findAll(pageable);
        HttpHeaders headers = PaginationUtil
            .generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * Get a specific credit limit request by ID.
     *
     * @param buyerId buyer ID
     * @param id      credit limit request ID
     * @return the credit limit request or 404 if not found
     */
    @GetMapping("/buyers/{buyerId}/credit-limit-requests/{id}")
    public ResponseEntity<CreditLimitRequestDTO> getCreditLimitRequest(
        @PathVariable(value = "buyerId") UUID buyerId,
        @PathVariable UUID id) {
        log.debug("REST request to get CreditLimitRequest : {}", id);
        Optional<CreditLimitRequestDTO> creditLimitRequestDTO = creditLimitRequestService.findOne(id);
        return ResponseUtil.wrapOrNotFound(creditLimitRequestDTO);
    }


    /**
     * Get the insurer's decision for a credit limit request.
     *
     * @param buyerId    buyer id
     * @param deliveryId targeted insurer decision
     * @return updated buyer with the new rawDecision
     */
    @GetMapping("/buyers/{buyerId}/insurer-decision/{deliveryId}")
    public ResponseEntity<BuyerFromInsurerDTO> getInsurerDecision(@PathVariable UUID buyerId, @PathVariable String deliveryId) {
        log.debug("REST request to get insurer decision data for the buyer : {} - with order id : {}", buyerId, deliveryId);

        return ResponseUtil.wrapOrNotFound(
            buyerRepository.findById(buyerId)
                .map(buyer -> creditLimitRequestService.updateInsurerDecision(buyer, deliveryId))
        );
    }
}
