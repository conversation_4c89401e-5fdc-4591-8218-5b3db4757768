{"type": "module", "scripts": {"build": "quasar build", "dev": "quasar dev", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "lint": "eslint --ext .js,.ts,.vue ./", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@fontsource/noto-sans": "^5.2.6", "@quasar/extras": "^1.16.11", "axios": "^1.8.2", "deepmerge": "^4.3.1", "flag-icons": "^7.2.3", "papaparse": "^5.4.1", "pinia": "^2.1.7", "quasar": "^2.18.1", "vue": "^3.5.13", "vue-i18n": "^9.14.3", "vue-router": "^4.3.2", "vue3-apexcharts": "^1.5.2"}, "devDependencies": {"@eslint/js": "9.26.0", "@quasar/app-vite": "^2.0.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@vitejs/plugin-vue": "5.2.3", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "autoprefixer": "10.4.21", "console-browserify": "^1.2.0", "date-fns": "3.6.0", "eslint": "9.26.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-cypress": "4.3.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "10.1.0", "http-errors": "^2.0", "lodash": "^4.17.21", "postcss": "^8.4.14", "prettier": "^3.2.5", "vue-eslint-parser": "^9.4.2"}, "productName": "afakto"}