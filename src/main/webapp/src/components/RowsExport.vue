<template>
  <q-btn icon="o_file_download" :label="$t('entity.action.export')" @click="exportRows" />
</template>

<script setup>
import Papa from 'papaparse';

import { api } from 'boot/axios';
import { exportFile } from 'quasar';

import { filtersToQueryParams } from 'src/util/filtering';

const props = defineProps({
  baseApiUrl: {
    required: true,
    type: String
  },
  columns: {
    required: true,
    type: Array
  },
  filters: {
    required: true,
    type: Object
  },
  pagination: {
    required: true,
    type: Object
  },
  visibleColumns: {
    required: true,
    type: Array
  }
});

const fetchRows = async () => {
  const { sortBy, descending } = props.pagination;
  const queryParams = filtersToQueryParams(props.filters);
  return (
    await api.get(props.baseApiUrl, {
      params: {
        size: 10000,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...queryParams
      }
    })
  ).data;
};

const generateFileName = () =>
  [new Date().toISOString().split('T')[0], window.location.pathname.replace('/', ''), ...Object.keys(props.filters)].join('-');

// Generate a csv file from the rows
const exportRows = async () => {
  // Generate headers
  const headers = props.columns.filter(col => props.visibleColumns.includes(col.name)).map(col => col.label || col.name);

  // Fetch and transform data in one go
  const data = (await fetchRows()).map(row =>
    props.columns
      .filter(col => props.visibleColumns.includes(col.name))
      .map(col => (typeof col.field === 'function' ? col.field(row) : col.field ? row[col.field] : row[col.name]))
  );

  exportFile(generateFileName() + '.csv', Papa.unparse({ fields: headers, data: data }), 'text/csv');
};
</script>
