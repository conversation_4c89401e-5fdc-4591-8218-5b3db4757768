<template>
  <q-input
    filled
    :input-class="['currency', 'number', 'percent'].includes(type) ? 'text-right' : ''"
    :label="label"
    lazy-rules
    :model-value="transformFrom(modelValue)"
    :readonly="readonly"
    :regular-expression="['currency', 'number', 'percent'].includes(type) ? '^-?\\d+(\\.\\d+)?$' : null"
    :rules="rules"
    :suffix="!suffix && type === 'percent' ? '%' : suffix"
    :type="type != 'currency' && type != 'percent' && ['email', 'tel'].includes(type) ? type : 'text'"
    @update:model-value="$emit('update:modelValue', transformTo($event))"
  >
    <template #append>
      <slot name="append" />
    </template>
    <template #after>
      <slot name="after" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { i18n } from 'boot/i18n';
import type { PropType } from 'vue';
import { useI18n } from 'vue-i18n';

import { parseLocaleNumber } from 'src/util/numberUtils';

const { locale, n } = useI18n();

defineEmits(['update:modelValue']);

const props = defineProps({
  label: {
    type: String,
    required: true
  },
  modelValue: {
    type: [Number, String],
    default: null
  },
  readonly: Boolean,
  rules: {
    type: Object as PropType<any[]>,
    default: null
  },
  suffix: {
    type: String,
    default: ''
  },
  tooltip: {
    type: String,
    default: ''
  },
  type: {
    type: String as PropType<
      'currency' | 'email' | 'file' | 'number' | 'password' | 'percent' | 'search' | 'tel' | 'text' | 'textarea' | 'time' | 'url'
    >,
    default: 'text'
  }
});

const transformFrom = (value: any) => {
  if (!value) return value;

  switch (props.type) {
    case 'currency':
      return formatWithThousandSeparators(value);
    case 'percent':
      return n(value, props.type);
    case 'number':
      return n(value);
    default:
      return value;
  }
};

const formatWithThousandSeparators = (value: any) => {
  if (!value) return value;
  return Number(value).toLocaleString(i18n.global.locale);
};

const transformTo = (value: any) => {
  if (!value) return value;

  switch (props.type) {
    case 'currency':
    case 'number':
    case 'percent':
      return parseLocaleNumber(value, locale.value);
    default:
      return value;
  }
};
</script>
