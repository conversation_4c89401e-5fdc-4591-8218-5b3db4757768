<template>
  <template v-for="filteredColumn in filteredColumns" :key="filteredColumn">
    <q-select
      v-if="modelValue[filteredColumn].operation === 'specified'"
      v-model="modelValue[filteredColumn].operation"
      borderless
      dense
      emit-value
      :label="columns.find(column => column.name === filteredColumn)?.label"
      map-options
      :options="operations.map(operation => ({ ...operation, label: operation.label.replace('∅', `∅ ${$t('global.form.empty')}`) }))"
      @update:model-value="() => (modelValue[filteredColumn].value = '')"
    />
    <q-select
      v-else-if="getFilter(filteredColumn).type === 'boolean'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value != null }"
      dense
      emit-value
      map-options
      :options="booleanOptions"
      :display-value="getBooleanDisplayValue(modelValue[filteredColumn].value)"
      :label="columns.find(column => column.name === filteredColumn)?.label"
      :title="columns.find(column => column.name === filteredColumn)?.label"
      stack-label
    />
    <q-select
      v-else-if="getFilter(filteredColumn).type === 'country'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value && modelValue[filteredColumn].value !== 'xx' }"
      dense
      emit-value
      :label="$t('afaktoApp.contract.country')"
      map-options
      :options="[{ name: $t('global.form.empty'), code: 'xx', iso: 'xx' }, ...countries].filter(c => c.iso)"
      option-label="name"
      option-value="code"
      stack-label
    >
      <template v-if="modelValue[filteredColumn].value" #prepend>
        <span :class="`fi fi-${modelValue[filteredColumn].value}`" />
      </template>
      <template #option="scope">
        <q-item v-bind="scope.itemProps" dense>
          <q-item-section side>
            <span :class="`fi fi-${scope.opt.code}`" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.name }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- Display proper input depending on filter type -->
    <q-select
      v-else-if="getFilter(filteredColumn).type === 'currency'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      dense
      :label="columns.find(column => column.name === filteredColumn)?.label"
      multiple
      :options="allowedCurrencies"
      options-selected-class="text-bold"
      stack-label
      hide-bottom-space
    />
    <q-input
      v-else-if="getFilter(filteredColumn).type === 'date'"
      v-model="modelValue[filteredColumn].value"
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      borderless
      dense
      :label="columns.find(column => column.name === filteredColumn)?.label"
      type="date"
    >
      <template #prepend>
        <!-- Select a filter operation -->
        <q-select
          v-model="modelValue[filteredColumn].operation"
          borderless
          dense
          emit-value
          hide-dropdown-icon
          map-options
          :options="operations"
          @update:model-value="
            value => {
              if (value == 'specified') {
                modelValue[filteredColumn].value = 'false';
              }
            }
          "
        />
      </template>
    </q-input>
    <q-select
      v-else-if="getFilter(filteredColumn).type === 'enum'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      dense
      emit-value
      :label="columns.find(column => column.name === filteredColumn)?.label"
      map-options
      multiple
      :options="getFilter(filteredColumn).values"
      options-selected-class="text-bold"
      stack-label
    />
    <q-input
      v-else-if="getFilter(filteredColumn).type === 'number'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      dense
      input-class="text-right"
      :label="columns.find(column => column.name === filteredColumn)?.label"
      stack-label
      :type="modelValue[filteredColumn].operation != 'specified' ? 'number' : 'text'"
    >
      <template #prepend>
        <!-- Select a filter operation -->
        <q-select
          v-model="modelValue[filteredColumn].operation"
          borderless
          dense
          emit-value
          hide-dropdown-icon
          map-options
          :options="operations"
          @update:model-value="
            value => {
              if (value == 'specified') {
                modelValue[filteredColumn].value = 'false';
              }
            }
          "
        />
      </template>
    </q-input>
    <b-selector
      v-else-if="getFilter(filteredColumn).type === 'select'"
      v-model="modelValue[filteredColumn].value"
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      dense
      emit-value
      :filled="false"
      :label="columns.find(column => column.name === filteredColumn)?.label"
      multiple
      :option-label="getFilter(filteredColumn).optionLabel"
      :option-value="getFilter(filteredColumn).optionValue"
      :options-preload="true"
      options-selected-class="text-bold"
      stack-label
      :url="getFilter(filteredColumn).url"
    />
    <q-input
      v-else
      borderless
      class="new-filter"
      :class="{ 'new-filter-active': modelValue[filteredColumn].value?.length > 0 }"
      dense
      :label="columns.find(column => column.name === filteredColumn)?.label || filteredColumn"
      :model-value="modelValue[filteredColumn].value"
      @update:model-value="value => debouncedTextUpdate({ ...modelValue[filteredColumn], value }, filteredColumn)"
      stack-label
    />

    <a
      class="close cursor-pointer"
      @click="
        delFilter({ value: filteredColumn });
        filteredColumns = filteredColumns.filter(column => column !== filteredColumn);
      "
    >
      <q-icon name="close" />
    </a>
  </template>

  <div class="row items-center" v-if="filter">
    <q-select
      v-model="filteredColumns"
      class="add-filter"
      borderless
      :display-value="$t('entity.action.addfilter')"
      emit-value
      hide-dropdown-icon
      map-options
      multiple
      option-value="name"
      :options="columns"
      options-selected-class="text-bold"
      @add="addFilter"
      @remove="delFilter"
    >
      <template #prepend>
        <q-icon style="font-size: 16px" name="add" />
      </template>
    </q-select>
    <q-separator v-if="hasActiveFilters" vertical class="q-mx-sm" />
    <div v-if="hasActiveFilters" class="cursor-pointer reset-filter" @click="resetFilteredColumns">
      {{ $t('entity.action.reset') || 'Reset' }}
    </div>
  </div>
</template>

<script setup>
import { api } from 'boot/axios';
import countries from 'flag-icons/country.json';
import { debounce } from 'quasar';
import { computed, onMounted, ref, toRaw, watch } from 'vue';
import { useRoute } from 'vue-router';

import BSelector from 'components/BSelector.vue';

const route = useRoute();

const allowedCurrencies = ref([]);
const context = ref(route.query.context || '');
const modelValue = defineModel({ type: Object });

// Computed property to check if there are any active filters
const hasActiveFilters = computed(() => {
  // Check if there are any defined filters with values
  return Object.values(modelValue.value || {}).some(
    filter => filter !== undefined && filter.value !== '' && (!Array.isArray(filter.value) || filter.value.length)
  );
});

// Receive columns from parent component
const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  filter: {
    type: Boolean,
    default: true
  }
});

const operations = [
  { label: '=', value: 'equals' },
  { label: '!=', value: 'notEquals' },
  { label: '>', value: 'greaterThan' },
  { label: '<', value: 'lessThan' },
  { label: '>=', value: 'greaterThanOrEqual' },
  { label: '<=', value: 'lessThanOrEqual' },
  { label: '∅', value: 'specified' }
];

const booleanOptions = [
  { label: 'True', value: true },
  { label: 'False', value: false },
  { label: 'Clear', value: null }
];

const getBooleanDisplayValue = value => {
  if (value === true) return 'True';
  if (value === false) return 'False';
  return ''; // Return empty string for null value
};

const filteredColumns = ref([]);

// Define the key for storing filters in local storage
const localStorageKey = `${window.location.pathname.replaceAll('/', '')}${context.value}.filters`;

const addFilter = async ({ value }) => {
  const filter = getFilter(value);

  let operation = 'equals';
  let val;

  if (['currency', 'enum', 'select'].includes(filter?.type)) operation = 'in';
  else if (['text'].includes(filter?.type)) operation = 'contains';
  else if (['boolean'].includes(filter?.type)) val = true;

  modelValue.value[value] = { operation, field: filter.field, value: val };

  // Fetch allowed currencies if the filter type is currency
  if (filter?.type === 'currency' && !allowedCurrencies.value.length) {
    allowedCurrencies.value = (await api.get('/api/contracts/currencies')).data;
  }
};

const delFilter = ({ value }) => (modelValue.value[value] = undefined);

const resetFilteredColumns = () => {
  modelValue.value = {};
  filteredColumns.value = [];
  localStorage.removeItem(localStorageKey);
};

const filterCache = {};

const getFilter = name => {
  if (!filterCache[name]) filterCache[name] = props.columns?.find(column => column.name === name)?.filter || {};
  return filterCache[name];
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// On mount, load the visible columns from local storage if available,
// otherwise reset to the default
onMounted(async () => {
  // Get filters from local storage
  const storedFilters = localStorage.getItem(localStorageKey);
  if (storedFilters) {
    Object.entries(JSON.parse(storedFilters))
      // Only keep elements for which value is not empty
      .filter(([_key, value]) => value.value !== '' && (!Array.isArray(value.value) || value.value.length))
      .forEach(([key, value]) => (modelValue.value[key] = value));
  }

  // Reconstruct filters from query params
  for (const param in route.query) {
    if (!param.includes('.')) continue;
    const key = param.split('.')[0];
    const operation = param.split('.')[1];
    let value = route.query[param];
    value = value === 'true' ? true : value === 'false' ? false : value;
    modelValue.value[key] = { operation, value };
  }

  // To trigger the watcher
  modelValue.value = { ...modelValue.value };

  if (Object.values(modelValue.value).some(filter => filter?.field === 'orgId')) {
    // Otherwise the eventual organization might not be popuplated yet
    await delay(150);
  }

  filteredColumns.value = Object.keys(toRaw(modelValue.value));

  if (Object.values(modelValue.value).some(filter => filter?.field === 'currency') && !allowedCurrencies.value.length) {
    allowedCurrencies.value = (await api.get('/api/contracts/currencies')).data;
  }

  if (storedFilters || context.value || Object.keys(route.query).length || Object.keys(modelValue.value).length) {
    return;
  }

  resetFilteredColumns();
});

// Create debounced update function specifically for text input
const debouncedTextUpdate = debounce((newValue, field) => {
  modelValue.value[field] = newValue;
}, 300);

// No need to remove boolean filters when they're set to null

watch(
  () => modelValue.value,
  () => {
    if (Object.keys(modelValue.value).length === 0 && modelValue.value.constructor === Object) return;
    localStorage.setItem(localStorageKey, JSON.stringify(modelValue.value));
  },
  { deep: true }
);
</script>
