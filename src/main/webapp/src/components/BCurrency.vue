<template>
  <q-select
    v-model="modelValue"
    :label="label"
    :options="allowedCurrencies"
    :option-label="currency => (!currency && modelValue ? t('afaktoApp.user.me.none') : currency)"
    :readonly="readonly"
    :rules="rules"
  />
</template>

<script setup lang="ts">
import { api } from 'boot/axios';
import type { PropType } from 'vue';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const allowedCurrencies = ref<string[]>([]);
const modelValue = defineModel<string | undefined>({ required: true });

const props = defineProps({
  label: {
    type: String,
    default: 'Currency'
  },
  readonly: Bo<PERSON>an,
  rules: {
    type: Array as PropType<any[]>,
    default: () => []
  }
});

onMounted(async () => {
  try {
    const response = await api.get('/api/contracts/currencies');
    if (props.rules.find(rule => rule.name === 'required')) {
      allowedCurrencies.value = response.data;
    } else {
      allowedCurrencies.value = ['', ...response.data];
    }
  } catch (error) {
    console.error('Failed to fetch allowed currencies:', error);
  }
});
</script>
