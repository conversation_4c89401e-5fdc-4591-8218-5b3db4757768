<template>
  <q-space />

  <!-- Select component for choosing visible columns -->
  <q-select
    v-if="filter"
    v-model="modelValue"
    borderless
    class="buttonNeutral"
    :clearable="!pristine"
    :display-value="$t('entity.action.columns')"
    emit-value
    hide-dropdown-icon
    map-options
    multiple
    option-value="name"
    :options="columns"
    options-selected-class="text-bold"
    @clear="resetVisibleColumns"
    @update:model-value="setModelValue"
  >
    <template #prepend>
      <q-icon name="view_week" />
    </template>
  </q-select>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const context = ref(route.query.context || '');

// Define modelValue as a reactive array
const modelValue = defineModel({ type: Array });

const pristine = computed(
  () => JSON.stringify(modelValue.value) === JSON.stringify(props.columns?.filter(column => !column.hidden)?.map(column => column.name))
);

// Receive columns and filter props from parent component
const props = defineProps({
  columns: {
    type: Array,
    default: () => []
  },
  filter: {
    type: Boolean,
    default: true
  }
});

// Define the key for storing visible columns in local storage
const localStorageKey = `${window.location.pathname.replaceAll('/', '')}${context.value}.visibleColumns`;

// Function to save the current visible columns to local storage
const setModelValue = value => localStorage.setItem(localStorageKey, JSON.stringify(value));

// Function to reset visible columns to the default (all columns)
const resetVisibleColumns = () => {
  modelValue.value = props.columns.filter(column => !column.hidden).map(column => column.name);
  localStorage.removeItem(localStorageKey);
};

// On mount, load the visible columns from local storage if available,
// otherwise reset to the default
onMounted(async () => {
  if (context.value === '_unsecured') {
    modelValue.value = props.columns
      .filter(column => !column.hidden || column.name == 'invoiceFromFactor_amountUnsecured')
      .map(column => column.name);
    return;
  }

  const storedColumns = localStorage.getItem(localStorageKey);
  if (storedColumns) {
    modelValue.value = JSON.parse(storedColumns);
    return;
  }

  resetVisibleColumns();
});
</script>
