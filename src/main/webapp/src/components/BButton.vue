<template>
  <q-btn
    :loading="loading"
    :label="label"
    :disable="disable"
    :flat="look === 'primary' ? false : true"
    :color="look === 'primary' ? 'primary' : undefined"
    :dark="$q.dark.mode"
    :type="type"
    @click="onInternalClick"
  >
    <template #loading>
      <q-spinner-facebook></q-spinner-facebook>
    </template>
  </q-btn>
</template>

<script lang="ts">
import type { PropType } from 'vue';
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'BButton',
  props: {
    look: {
      type: String as PropType<'primary' | 'secondary'>,
      required: false,
      default: ''
    },
    type: {
      type: String,
      required: false,
      default: 'button'
    },
    disable: {
      type: Boolean,
      required: false
    },
    label: {
      type: String,
      required: true
    },
    onClick: {
      type: Function as PropType<(done: () => void) => void>,
      default: () => {
        console.log('click...');
      }
    },
    active: {
      type: Boolean
    }
  },

  setup(props) {
    const loading = ref(false);

    const onInternalClick = () => {
      loading.value = true;
      props.onClick!(() => (loading.value = false));
    };
    return { loading, onInternalClick };
  }
});
</script>

<style scoped>
[dark='true']:not([color='primary']) {
  background: rgb(60, 60, 60);
}
[dark='false']:not([color='primary']) {
  background: rgb(241, 241, 241);
}
</style>
