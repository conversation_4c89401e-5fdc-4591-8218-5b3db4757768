import { defineStore } from 'pinia';

import type { IUser } from 'src/shared/model/user.model';

export const useAuthenticationStore = defineStore('authentication', {
  state: () => ({
    _account: null as IUser | null,
    _logoutTimer: null as ReturnType<typeof setTimeout> | null
  }),
  getters: {
    account: state => state._account,
    isAuthenticated: state => state._account != null,
    hasRoleWriter: state => state._account && state._account.authorities && state._account.authorities.includes('ROLE_WRITER'),
    hasRoleConfig: state => state._account && state._account.authorities && state._account.authorities.includes('ROLE_CONFIG'),
    hasRoleAdmin: state => state._account && state._account.authorities && state._account.authorities.includes('ROLE_ADMIN')
  },
  actions: {
    login(data: IUser) {
      this._account = data;
    }
  }
});

export type AuthenticationStore = ReturnType<typeof useAuthenticationStore>;
