import countries from 'flag-icons/country.json';
import { computed, ref } from 'vue';
import type { Composer } from 'vue-i18n';

export function findIdType(i18n: Composer) {
  const countryFilter = ref({});
  const identifier = ref(null);
  const identifierType = ref<{ country?: string; type?: string; label?: string }>({});
  const { t, tm, te } = i18n;

  const filteredCountries = computed(() => {
    if (Object.keys(countryFilter.value).length === 0) return countries.filter(c => c.iso);
    else return countries.filter(c => c.iso && countryFilter.value[c.code]);
  });

  const matchIdType = async (inputValue: string) => {
    const matches = [];
    const countryKeys = Object.keys(tm('afaktoApp.NumberType'));

    for (const country of countryKeys) {
      const types = tm(`afaktoApp.NumberType.${country}`) as Record<string, any>;

      for (const typeKey in types) {
        const regexPath = `afaktoApp.NumberType.${country}.${typeKey}.regex`;
        if (!te(regexPath)) continue;

        const regexPattern = tm(regexPath);
        const regex = new RegExp(regexPattern);
        if (!regex.test(inputValue)) continue;

        matches.push({
          country: country.toLowerCase(),
          type: typeKey,
          fullKey: `${country}:${typeKey}`,
          label: t(`afaktoApp.NumberType.${country}.${typeKey}.label`)
        });
      }
    }

    return matches;
  };

  const evaluateIdentifier = async (inputValue: string) => {
    const matches = await matchIdType(inputValue);

    if (matches.length === 0) {
      identifierType.value = {};
      countryFilter.value = {};
    } else if (matches.length === 1) {
      identifierType.value = { country: matches[0].country, type: matches[0].type, label: matches[0].label };
      countryFilter.value = {};
    } else {
      identifierType.value = {};
      for (const match of matches) {
        countryFilter.value[match.country] = {
          type: match.type,
          label: match.label
        };
      }
    }
  };

  const checkCountry = (countryCode: string) => {
    const match = countryFilter.value[countryCode];
    if (match) identifierType.value = match;
  };

  return {
    identifier,
    identifierType,
    countryFilter,
    filteredCountries,
    evaluateIdentifier,
    checkCountry
  };
}
