import { api } from 'boot/axios';
import { loadTranslation } from 'boot/i18n';
import { Cookies } from 'quasar';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';

export const AUTH_PATH = '/oauth2/authorization/oidc';
const LOGOUT_API = '/api/logout';
const ACCOUNT_API = '/api/account';

export const beforeEachAuth = async (to: RouteLocationNormalized, _from: RouteLocationNormalized, next: NavigationGuardNext) => {
  // Get locale from browser data:
  const locale = navigator.language.split('-')[0] || 'en';

  if (to.meta?.public) {
    await loadTranslation(locale);
    return next();
  }

  const store = useAuthenticationStore();

  if (store.isAuthenticated) {
    if (!Cookies.has('XSRF-TOKEN')) {
      next({ path: '/', query: { redirect: to.fullPath } });
      return;
    }
    return next();
  }

  const accountResponse = await api.get(ACCOUNT_API);
  if (!accountResponse || accountResponse.status === 401) {
    window.location.href = AUTH_PATH;

    return next(false); // aborts the current navigation
  }

  store.login(accountResponse.data);
  await loadTranslation(accountResponse.data.langKey || locale);

  next();
};

export const authLogout = async (store: any) => {
  try {
    const response = await api.post(LOGOUT_API);
    window.location.href = response.data.logoutUrl;
  } catch (error) {
    console.error('Logout failed:', error);
  }
};
