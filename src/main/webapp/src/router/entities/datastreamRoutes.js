export const datastreamRoutes = [
  {
    path: '/datastreams',
    meta: { public: false },
    component: () => import('pages/entities/datastream/DatastreamList.vue')
  },
  {
    path: '/datastreams/:id',
    meta: { public: false },
    component: () => import('pages/entities/datastream/DatastreamDetails.vue')
  },
  {
    path: '/datastreams/upload',
    meta: { public: false },
    component: () => import('pages/entities/datastream/DatastreamUpload.vue')
  }
];
