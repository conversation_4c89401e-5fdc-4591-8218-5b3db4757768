export const factorInstitutionRoutes = [
  {
    path: '/factor-institutions',
    meta: { public: false },
    component: () => import('pages/entities/factor-institution/FactorInstitutionsList.vue')
  },
  {
    path: '/factor-institutions/new',
    meta: { public: false },
    component: () => import('pages/entities/factor-institution/FactorInstitutionDetails.vue')
  },
  {
    path: '/factor-institutions/:id/edit',
    meta: { public: false },
    component: () => import('pages/entities/factor-institution/FactorInstitutionDetails.vue')
  }
];
