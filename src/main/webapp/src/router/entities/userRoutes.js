export const userRoutes = [
  {
    path: '/users',
    meta: { public: false },
    component: () => import('pages/entities/user/UsersList.vue')
  },
  {
    path: '/users/new',
    meta: { public: false },
    component: () => import('pages/entities/user/UserDetails.vue')
  },
  {
    path: '/users/:id',
    meta: { public: false },
    component: () => import('pages/entities/user/UserDetails.vue')
  }
];
