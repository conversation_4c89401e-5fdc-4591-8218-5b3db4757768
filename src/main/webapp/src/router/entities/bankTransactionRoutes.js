export const bankTransactionRoutes = [
  {
    path: '/bank-transactions',
    meta: { public: false },
    component: () => import('pages/entities/bank-transaction/BankTransactionsList.vue')
  },
  {
    path: '/bank-transactions-with-offset',
    meta: { public: false },
    component: () => import('pages/entities/bank-transaction/BankTransactionsListWithOffset.vue')
  },
  {
    path: '/bank-transactions/new',
    meta: { public: false },
    component: () => import('pages/entities/bank-transaction/BankTransactionDetails.vue')
  },
  {
    path: '/bank-transactions/:id',
    meta: { public: false },
    component: () => import('pages/entities/bank-transaction/BankTransactionDetails.vue')
  }
];
