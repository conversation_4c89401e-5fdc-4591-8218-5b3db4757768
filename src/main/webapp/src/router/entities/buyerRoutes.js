export const buyerRoutes = [
  {
    path: '/buyers',
    meta: { public: false },
    component: () => import('pages/entities/buyer/BuyersList.vue')
  },
  {
    path: '/buyers/:id/credit-limit-requests',
    meta: { public: false },
    component: () => import('pages/entities/credit-limit-request/CreditLimitRequests.vue')
  },
  {
    path: '/buyers/upload',
    meta: { public: false },
    component: () => import('pages/entities/buyer/BuyerUpload.vue')
  },
  {
    path: '/buyers/new',
    meta: { public: false },
    component: () => import('pages/entities/buyer/BuyerDetails.vue')
  },
  {
    path: '/buyers/:id',
    meta: { public: false },
    component: () => import('pages/entities/buyer/BuyerDetails.vue')
  }
];
