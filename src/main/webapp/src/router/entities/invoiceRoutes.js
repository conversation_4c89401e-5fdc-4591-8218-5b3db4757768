export const invoiceRoutes = [
  {
    path: '/invoices',
    meta: { public: false },
    component: () => import('pages/entities/invoice/InvoicesList.vue')
  },
  {
    path: '/invoices/upload',
    meta: { public: false },
    component: () => import('pages/entities/invoice/InvoiceUpload.vue')
  },
  {
    path: '/invoices/new',
    meta: { public: false },
    component: () => import('pages/entities/invoice/InvoiceDetails.vue')
  },
  {
    path: '/invoices/:id',
    meta: { public: false },
    component: () => import('pages/entities/invoice/InvoiceDetails.vue')
  }
];
