export const categoryToAccountRoutes = [
  {
    path: '/category-to-accounts',
    meta: { public: false },
    component: () => import('pages/entities/category-to-account/CategoryToAccountsList.vue')
  },
  {
    path: '/category-to-accounts/new',
    meta: { public: false },
    component: () => import('pages/entities/category-to-account/CategoryToAccountDetails.vue')
  },
  {
    path: '/category-to-accounts/:id',
    meta: { public: false },
    component: () => import('pages/entities/category-to-account/CategoryToAccountDetails.vue')
  }
];
