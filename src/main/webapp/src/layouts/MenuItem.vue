<template>
  <q-item :class="{ 'q-router-link--exact-active': isActive(to) }" exact :to="to" v-ripple>
    <q-item-section avatar><q-icon class="text-neutralHigher" :name="`o_${icon}`" /></q-item-section>
    <q-item-section class="text-neutralHighest">{{ $t(i18nKey) }}</q-item-section>
  </q-item>
</template>

<script setup>
import { useRoute } from 'vue-router';

const route = useRoute();

defineProps({
  i18nKey: { type: String, required: true },
  icon: { type: String, required: true },
  miniState: Boolean,
  to: { type: String, required: true }
});

const isActive = to => (to != '/' || route.path == '/') && route.path.startsWith(to);
</script>
