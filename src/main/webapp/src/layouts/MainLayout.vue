<template>
  <q-layout :class="store.account?.authorities" view="lHh lpR fFf">
    <q-header class="bg-backgroundSecondary">
      <q-toolbar no-margin no-padding>
        <h2 v-if="!miniState" class="no-margin">
          <q-btn aria-label="Menu" dense flat icon="menu" round @click="open" />
        </h2>
        <q-space />
        <h3 v-if="appVersion === 'DEV'" class="dev">{{ appVersion }}</h3>
        <h3 v-else-if="store.hasRoleAdmin && store.account.firstName === 'Emmanuel'" class="prod">production - admin</h3>
        <q-space />
        <UserComp />
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      bordered
      class="bg-backgroundSecondary column no-wrap"
      :mini="miniState && !overlay"
      :mini-to-overlay="miniState && overlay"
      show-if-above
      :width="200"
      @mouseenter="overlay = true"
      @mouseleave="overlay = false"
    >
      <q-item class="logo main-logo" style="padding-top: 0px; height: 50px !important">
        <q-tooltip anchor="center left" class="bg-primary" :offset="[10, 10]" self="center right">
          {{ appVersion }}
        </q-tooltip>
        <q-item-section avatar>
          <img class="logo" src="/favicon.png" width="32" height="" />
        </q-item-section>
        <q-item-section>
          <h2 class="text-neutralHigher" style="margin-left: -3px; margin-top: 20.916px; font-weight: bolder">afakto</h2>
        </q-item-section>
      </q-item>

      <q-separator inset style="clear: both" />

      <template v-for="menuItem in menuItems" :key="menuItem.to">
        <MenuItem :i18n-key="menuItem.i18nKey" :icon="menuItem.icon" :mini-state="miniState" :to="menuItem.to" />
      </template>

      <q-space />

      <q-expansion-item v-if="store.hasRoleAdmin" icon="o_explore" :label="$t('global.menu.admin.main')">
        <q-separator inset />
        <template v-for="menuItem in adminMenuItems" :key="menuItem.to">
          <MenuItem dense :i18n-key="menuItem.i18nKey" :icon="menuItem.icon" :mini-state="miniState" :to="menuItem.to" />
        </template>
        <q-separator inset />
      </q-expansion-item>

      <template v-if="store.hasRoleConfig">
        <template v-for="menuItem in settingsAndHelpDataMenuItems" :key="menuItem.to">
          <MenuItem :i18n-key="menuItem.i18nKey" :icon="menuItem.icon" :mini-state="miniState" :to="menuItem.to" />
        </template>
      </template>

      <!--MenuItem i18n-key="global.menu.help" icon="help" :mini-state="miniState" to="/help" /-->
    </q-drawer>

    <q-page-container :key="componentKey" :class="store.account?.preferences?.DENSE_MODE === 'true' ? 'dense' : ''">
      <router-view v-slot="{ Component }">
        <transition class="q-pa-md" duration="150" enter-active-class="animated fadeIn" leave-active-class="animated fadeOut" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { ref, watch } from 'vue';

import UserComp from 'pages/subcomponents/UserComp.vue';
import MenuItem from 'src/layouts/MenuItem.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';

const $q = useQuasar();

const componentKey = ref(0);
const leftDrawerOpen = ref($q.platform.is?.desktop);
const overlay = ref(false);
const miniState = ref($q.platform.is?.desktop);
const store = useAuthenticationStore();

watch(
  () => store._account?.preferences?.ROWS_PER_PAGE,
  () => componentKey.value++
);

// Watches for changes in the reference currency
// applies a main part reload, after a delay so that the new setting will be taken into account
watch(
  () => store._account?.preferences?.REFERENCE_CURRENCY,
  () => setTimeout(() => componentKey.value++, 500)
);

const open = () => {
  leftDrawerOpen.value = $q.platform.is?.desktop || !leftDrawerOpen.value;
  miniState.value = $q.platform.is?.desktop && !miniState.value;
};

if (store._account.preferences.DARK_MODE === undefined) store._account.preferences.DARK_MODE = 'auto';
$q.dark.set(store._account.preferences.DARK_MODE === 'auto' ? 'auto' : store._account.preferences.DARK_MODE === 'true');

const appVersion = process.env.APP_VERSION;

const menuItems = [
  {
    to: '/',
    i18nKey: 'global.menu.entities.dashboard',
    icon: 'grid_view'
  },
  {
    to: '/contracts',
    icon: 'edit_note',
    i18nKey: 'global.menu.entities.contract'
  },
  {
    to: '/buyers?balance.notEquals=0',
    icon: 'store',
    i18nKey: 'global.menu.entities.buyer'
  },
  {
    to: '/invoices?balance.notEquals=0',
    icon: 'receipt',
    i18nKey: 'global.menu.entities.invoice'
  },
  {
    to: '/cessions',
    icon: 'done_all',
    i18nKey: 'global.menu.entities.cession'
  },
  {
    to: '/bank-transactions',
    icon: 'compare_arrows',
    i18nKey: 'global.menu.entities.bankTransaction'
  },
  ...[]
];

const adminMenuItems = [
  {
    to: '/factor-institutions',
    icon: 'account_balance',
    i18nKey: 'global.menu.entities.factorInstitution'
  },
  {
    to: '/external-credit-insurances',
    icon: 'medication',
    i18nKey: 'global.menu.entities.externalCreditInsurance'
  },
  {
    to: '/configuration',
    icon: 'settings',
    i18nKey: 'global.menu.admin.configuration'
  },
  {
    to: '/health',
    icon: 'health_and_safety',
    i18nKey: 'global.menu.admin.health'
  },
  {
    to: '/metrics',
    icon: 'analytics',
    i18nKey: 'global.menu.admin.metrics'
  },
  {
    to: '/logs',
    icon: 'text_snippet',
    i18nKey: 'global.menu.admin.logs'
  },
  ...[]
];

const settingsAndHelpDataMenuItems = [
  {
    to: '/companies',
    icon: 'apartment',
    i18nKey: 'global.menu.entities.company'
  },
  {
    to: '/users',
    icon: 'group',
    i18nKey: 'global.menu.entities.user'
  },
  {
    to: '/datastreams',
    icon: 'file_copy',
    i18nKey: 'global.menu.entities.datastream'
  },
  {
    to: '/docs',
    icon: 'api',
    i18nKey: 'global.menu.admin.apidocs'
  },
  ...[]
];
</script>

<style lang="scss" scoped>
.q-header {
  border-bottom: 1px solid $borderSecondary;
  .body--dark & {
    border-color: $borderSecondaryDark;
  }

  h3.dev,
  h3.prod {
    animation: moving-shadow 2s infinite;
    letter-spacing: 12px;
    margin: 0;
  }
  h3.prod {
    color: red;
  }

  @keyframes moving-shadow {
    0% {
      text-shadow: 0 0 0 lightgreen;
    }
    25% {
      text-shadow: 0 0 0.5em lightgreen;
    }
    50% {
      text-shadow: 0 0 0.2em lightgreen;
    }
    75% {
      text-shadow: 0 0 0.3em lightgreen;
    }
    100% {
      text-shadow: 0 0 0 lightgreen;
    }
  }
}
</style>
