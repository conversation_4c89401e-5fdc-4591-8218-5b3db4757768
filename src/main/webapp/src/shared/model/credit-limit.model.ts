import type { CreditLimitStatus } from 'shared/model/enumerations/credit-limit-status.model';

export interface ICreditLimit {
  id?: number;
  totalOutstanding?: number | null;
  outstandingUnderFactor?: number | null;
  currentCreditLimit?: number | null;
  requestedCreditLimit?: number | null;
  date?: Date | null;
  status?: CreditLimitStatus | null;
}

export class CreditLimit implements ICreditLimit {
  constructor(
    public id?: number,
    public totalOutstanding?: number | null,
    public outstandingUnderFactor?: number | null,
    public currentCreditLimit?: number | null,
    public requestedCreditLimit?: number | null,
    public date?: Date | null,
    public status?: CreditLimitStatus | null
  ) {}
}
