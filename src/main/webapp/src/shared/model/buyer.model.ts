import type { IAddress } from 'shared/model/address.model';
import type { IContact } from 'shared/model/contact.model';
import type { NumberType } from 'shared/model/enumerations/number-type.model';
import type { Invoice } from 'shared/model/invoice.model';
import type { IPaymentTerms } from 'shared/model/payment-terms.model';

export type IBuyer = {
  id: number;
  code: string;
  name: string | null;
  numberType: NumberType;
  number: string;
  address?: IAddress;
  contact?: IContact;
  paymentTerms?: IPaymentTerms;
  invoices?: Invoice[];
};
