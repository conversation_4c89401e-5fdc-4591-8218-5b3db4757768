import type { <PERSON><PERSON><PERSON><PERSON> } from 'shared/model/buyer.model';
import type { ICreditLimit } from 'shared/model/credit-limit.model';
import type { InvoiceType } from 'shared/model/enumerations/invoice-type.model';

export type Invoice = {
  id?: number;
  buyer: IBuyer;
  type: InvoiceType;
  invoiceNumber: string;
  date: Date;
  dueDate: Date;
  currency: string;
  amount: number;
  balance: number;
  creditLimit?: ICreditLimit;
  excluded?: boolean;
  exclusionReason?: string;
  underFactor?: boolean;
};
