import type { IB<PERSON><PERSON> } from 'shared/model/buyer.model';

export interface IContact {
  id?: number;
  name?: string | null;
  phone?: string | null;
  email?: string | null;
  buyer?: IBuyer;
}

export class Contact implements IContact {
  constructor(
    public id?: number,
    public name?: string | null,
    public phone?: string | null,
    public email?: string | null,
    public buyer?: <PERSON><PERSON><PERSON><PERSON>
  ) {}
}
