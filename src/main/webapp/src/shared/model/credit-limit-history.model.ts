import type { <PERSON><PERSON><PERSON><PERSON> } from 'shared/model/buyer.model';
import type { ICreditLimit } from 'shared/model/credit-limit.model';

export interface ICreditLimitHistory {
  id?: number;
  startDate?: Date | null;
  endDate?: Date | null;
  creditLimit?: ICreditLimit;
  buyer?: IBuyer | null;
}

export class CreditLimitHistory implements ICreditLimitHistory {
  constructor(
    public id?: number,
    public startDate?: Date | null,
    public endDate?: Date | null,
    public creditLimit?: ICreditLimit,
    public buyer?: IBuyer | null
  ) {}
}
