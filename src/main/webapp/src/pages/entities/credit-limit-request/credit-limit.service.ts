import { api } from 'boot/axios';

const baseApiUrl = '/api/buyers';

export interface CreditLimitRequest {
  id: null | string;
  buyer: null | Buyer;
}

export interface Buyer {
  id: null | string;
  name: null | string;
}

export interface ICreditLimitRequestService {
  getList(page: number, size: number, sort: string): Promise<any>;
  getListForBuyer(buyerId: string, page: number, size: number, sort: string): Promise<any>;
  request(buyerId: string, creditLimitRequest: CreditLimitRequest): Promise<any>;
}

class CreditLimitRequestServiceImpl implements ICreditLimitRequestService {
  public request(buyerId: string, creditLimitRequest: CreditLimitRequest) {
    return api({
      method: creditLimitRequest.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: [buyerId, 'credit-limit-requests', creditLimitRequest.id].filter(Boolean).join('/'),
      data: creditLimitRequest
    });
  }

  public getList(page: number, size: number, sort: string): Promise<CreditLimitRequest> {
    return api.get('api/credit-limit-requests', {
      params: {
        page: page,
        size: size,
        sort: sort
      }
    });
  }

  public getListForBuyer(buyerId: string, page: number, size: number, sort: string): Promise<CreditLimitRequest> {
    return api.get(`${baseApiUrl}/${buyerId}/credit-limit-requests`, {
      params: {
        page: page,
        size: size,
        sort: sort
      }
    });
  }
}

const CreditLimitRequestService = new CreditLimitRequestServiceImpl() as ICreditLimitRequestService;

export default CreditLimitRequestService;
