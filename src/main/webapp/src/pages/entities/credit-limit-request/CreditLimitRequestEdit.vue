<template>
  <q-card>
    <q-form @submit.prevent="onSubmit">
      <q-card-section>
        <b-input v-model="entity.requestedAmount" :label="$t('afaktoApp.creditLimit.amount')" type="currency">
          <template #append>
            <b-currency
              v-model="entity.currency"
              :filled="false"
              :label="$t('afaktoApp.creditLimit.currency')"
              :readonly="!!props.buyer.currency"
            />
          </template>
          <template #after>
            <div v-if="!mustBeThousandRule()" class="text-negative text-caption q-mt-xs">
              {{ $t('afaktoApp.creditLimitRequest.detail.divisibleAmount', { amount: Number(1000).toLocaleString(i18n.global.locale) }) }}
            </div>
          </template>
        </b-input>
      </q-card-section>

      <q-card-actions align="center">
        <b-button class="buttonNeutral" v-close-popup icon="close" :label="$t('entity.action.cancel')" />
        <b-button class="buttonBrand" :disabled="!mustBeThousandRule()" icon="send" :label="$t('entity.action.submit')" type="submit" />
      </q-card-actions>
    </q-form>
  </q-card>
</template>

<script setup>
import { api } from 'boot/axios';
import { i18n } from 'boot/i18n';
import { useQuasar } from 'quasar';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import useNotifications from 'src/util/useNotifications.js';

const baseApiUrl = '/api/credit-limit-requests';
const { notifyError } = useNotifications();
const $q = useQuasar();
const { t } = useI18n();

const props = defineProps({
  buyer: Object,
  contract: Object
});
const emit = defineEmits(['update:dialog', 'credit-limit-requested']);
const entity = ref({
  buyer: props.buyer,
  requestedAmount: props.buyer.creditLimit?.amount,
  currency: props.contract.financialInformation.currency || 'EUR'
});

const mustBeThousandRule = () => entity.value.requestedAmount % 1000 === 0;

const onSubmit = async () => {
  if (!mustBeThousandRule()) return;

  try {
    const result = await api({
      method: 'post',
      baseURL: baseApiUrl,
      data: entity.value
    });

    $q.notify({
      icon: 'done',
      message: t(`afaktoApp.creditLimitRequest.created.${result.data.status}`),
      type: 'positive'
    });
    emit('credit-limit-requested', result.data);
    emit('update:dialog', false);
  } catch (error) {
    emit('update:dialog', true);
    notifyError(error);
  }
};
</script>
