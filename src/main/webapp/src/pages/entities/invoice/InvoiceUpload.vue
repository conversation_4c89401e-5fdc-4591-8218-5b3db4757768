<template>
  <q-page class="flex flex-center">
    <b-title :model-value="$t('afaktoApp.invoice.upload.title')" />

    <q-page-sticky position="top-right" :offset="[18, 18]">
      <q-btn color="primary" icon="close" to="/invoices">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-page-sticky>

    <q-uploader accept=".csv,.xsl,.xlsx" hide-upload-btn :label="$t('afaktoApp.invoice.upload.subtitle')" multiple @added="added">
      <template v-if="uploadResponses" #list>
        <q-list separator>
          <q-item v-for="uploadResponse in uploadResponses" :key="uploadResponse.name">
            <q-item-section>
              <q-item-label class="full-width ellipsis">{{ uploadResponse.name }}</q-item-label>

              <!-- Display created and updated entities -->
              <q-item-label v-if="uploadResponse.inserts" caption>
                {{ $t('afaktoApp.invoice.upload.created', { count: uploadResponse.inserts }) }}
              </q-item-label>
              <q-item-label v-if="uploadResponse.updates" caption>
                {{ $t('afaktoApp.invoice.upload.updated', { count: uploadResponse.updates }) }}
              </q-item-label>
              <q-item-label v-if="uploadResponse.deletes" caption>
                {{ $t('afaktoApp.invoice.upload.deleted', { count: uploadResponse.deletes }) }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <q-btn class="full-width" color="primary" icon="close" :label="$t('entity.action.close')" to="/invoices" />
      </template>
    </q-uploader>

    <!-- Display the response messages -->
    <template v-for="uploadResponse in uploadResponses" :key="uploadResponse.name">
      <q-table
        v-if="uploadResponse.failures.length"
        :columns="columns"
        :rows="uploadResponse.failures.sort((a: object, b: object) => a.line - b.line)"
        :title="uploadResponse.name"
      >
        <template #body="props">
          <q-tr class="cursor-pointer" :props="props" @click="props.row.expand = !props.row.expand">
            <q-td align="right" auto-width>{{ props.row.line }}</q-td>
            <q-td>{{ props.row.message }}</q-td>
            <q-td auto-width>
              <q-btn color="accent" dense :icon="props.row.expand ? 'expand_less' : 'expand_more'" round />
            </q-td>
          </q-tr>
          <q-tr v-show="props.row.expand" :props="props">
            <q-td colspan="3">
              <div class="text-left">{{ props.row.raw }}</div>
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </template>

    <q-page-sticky position="bottom">
      <q-tooltip>
        <q-icon name="help" />
        {{ $t('afaktoApp.invoice.upload.headers_help') }}
      </q-tooltip>
      <p><q-icon name="help" /> {{ $t('afaktoApp.invoice.upload.headers') }}:</p>
      <pre class="text-caption">COMPANY_CODE,BUYER_CODE,INV_TYPE,INV_NUMBER,INV_DATE,DUE_DATE,CURRENCY,AMOUNT,BALANCE,PAYMENT_METHOD</pre>
    </q-page-sticky>

    <q-page-sticky v-if="!uploadResponses" position="bottom-right">
      <q-card>
        <q-list>
          <q-item class="text-h6">
            <q-item-section avatar><q-icon name="help" /></q-item-section>
            <q-item-section>{{ $t('afaktoApp.invoice.upload.samples') }}</q-item-section>
          </q-item>
          <q-item v-ripple clickable href="/samples/invoiceUpload/invoices.csv">
            <q-item-section avatar><q-icon name="file_download" /></q-item-section>
            <q-item-section>invoices.csv</q-item-section>
          </q-item>
          <q-item v-ripple clickable href="/samples/invoiceUpload/invoices2.csv">
            <q-item-section avatar><q-icon name="file_download" /></q-item-section>
            <q-item-section>invoices2.csv</q-item-section>
          </q-item>
          <q-item v-ripple clickable href="/samples/invoiceUpload/IMPORT_FILE.xlsx">
            <q-item-section avatar><q-icon name="file_download" /></q-item-section>
            <q-item-section>IMPORT_FILE.xlsx</q-item-section>
          </q-item>
        </q-list>
      </q-card>
    </q-page-sticky>
  </q-page>
</template>

<script setup lang="ts">
import { api } from 'boot/axios';
import { computed, ref } from 'vue';

import useNotifications from 'src/util/useNotifications.js';

const { notifyError } = useNotifications();

const uploadResponses = ref<any>();

const columns = computed(() => [
  {
    name: 'Line',
    label: 'Line',
    field: 'line',
    align: 'right'
  },
  {
    name: 'Reason',
    label: 'Reason',
    field: 'message',
    align: 'left',
    sortable: true
  },
  { align: 'right' }
]);

const added = async (files: any) => {
  const fileData = new FormData();
  for (const file of files) {
    fileData.append('files', file);
  }

  try {
    const response = await api.post('/api/invoices/upload', fileData);
    uploadResponses.value = response.data;
  } catch (err) {
    notifyError(err);
  }
};
</script>
