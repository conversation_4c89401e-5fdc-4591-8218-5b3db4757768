<template>
  <q-drawer overlay show-if-above side="right">
    <header>
      <q-btn class="buttonNeutral" icon="close" @click="$emit('close')" />
      <q-item-section>
        <q-item-label class="show-label">
          <q-icon name="o_receipt" />
          {{ modelValue?.type.replaceAll('_', ' ') }}
        </q-item-label>
        <q-item-label class="text-bold">
          #{{ modelValue?.invoiceNumber }}
        </q-item-label>
        <q-item-label class="text-neutralHigher">
          {{ modelValue?.buyer?.name }}
        </q-item-label>
      </q-item-section>
    </header>

    <q-list>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.number') }}</q-item-section>
        <q-item-section avatar class="self-center">{{ modelValue?.invoiceNumber }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.date') }}</q-item-section>
        <q-item-section avatar class="self-center">{{ modelValue?.date }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.dueDate') }}</q-item-section>
        <q-item-section avatar class="self-center">{{ modelValue?.dueDate }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.currency') }}</q-item-section>
        <q-item-section avatar>{{ modelValue?.currency }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.amount') }}</q-item-section>
        <q-item-section avatar>{{ $n(modelValue?.amount, 'currency', { currency: modelValue?.currency }) }}
        </q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.balance') }}</q-item-section>
        <q-item-section avatar>{{ $n(modelValue?.balance, 'currency', { currency: modelValue?.currency }) }}
        </q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.reconciliationJournal') }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.invoice.status') }}</q-item-section>
        <div class="row q-gutter-sm">
          <q-item-section avatar
                          :class="[modelValue?.underFactor ? 'positive-show-label' : 'negative-show-label', 'self-center']">
            {{ modelValue?.underFactor ? $t('afaktoApp.invoice.underFactor') : $t('afaktoApp.invoice.notUnderFactor') }}
        </q-item-section>
          <q-item-section avatar
                          :class="[modelValue?.excluded ? 'negative-show-label' : 'positive-show-label', 'self-center']">
            {{ modelValue?.excluded ? $t('afaktoApp.invoice.excluded') : $t('afaktoApp.invoice.notExcluded') }}
          </q-item-section>
        </div>
      </q-item>
    </q-list>

    <footer>
      <q-btn class="buttonBrand" icon="edit" :label="$t('entity.action.edit')" :to="`/invoices/${modelValue.id}`" />
    </footer>
  </q-drawer>
</template>

<script setup>
defineEmits(['close']);

defineProps({ modelValue: { type: Object, required: true } });
</script>
