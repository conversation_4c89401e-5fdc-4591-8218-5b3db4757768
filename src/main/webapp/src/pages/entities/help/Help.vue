<template>
  <q-page class="q-pa-md">
    <b-title :model-value="$t('help.title')" />
    <div class="q-gutter-y-md containerWithoutTable" style="max-width: 100%">
      <q-card>
        <q-tabs
          v-model="tab"
          dense
          align="justify"
          class="text-grey"
          active-color="primary"
          indicator-color="action"
          narrow-indicator
          @click="SettingsTab"
        >
          <q-tab name="glossary" label="Glossary" />
          <q-tab name="supportContacts" label="Support Contacts" />
          <q-tab name="referenceLinks" label="Reference Links" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated>
          <q-tab-panel name="glossary">
            <div class="text-h6">Glossary</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>

          <q-tab-panel name="supportContacts">
            <div class="text-h6">Support Contacts</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>

          <q-tab-panel name="referenceLinks">
            <div class="text-h6">Reference Links</div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit.
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from 'vue';

const tab = ref('glossary');
</script>
