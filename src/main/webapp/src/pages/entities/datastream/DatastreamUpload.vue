<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.datastream.home.title') }} | {{ $t('afaktoApp.datastream.upload.title') }}</h1>

      <q-space />

      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy>
      <q-card>
        <q-card-section>
          <q-select
            v-if="hasRoleAdmin"
            v-model="entity.organization"
            emi
            filled
            :label="$t('afaktoApp.company.organization')"
            :options="organizations"
            option-label="displayName"
            option-value="orgId"
            :rules="[required]"
          >
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <img v-if="scope.opt.logoUrl" :alt="scope.opt.displayName" :src="scope.opt.logoUrl" style="height: 2.2em" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.displayName }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-if="entity.organization" #prepend>
              <img
                v-if="entity.organization.logoUrl"
                :alt="entity.organization.displayName"
                :src="entity.organization.logoUrl"
                style="height: 100%"
              />
            </template>
          </q-select>

          <q-select
            v-model="entity.type"
            filled
            :label="$t('afaktoApp.datastream.type')"
            map-options
            :options="Object.keys(DATASTREAM_HEADERS)"
            :option-label="item => $t(`afaktoApp.DatastreamType.${item}`)"
            :option-value="item => item"
            options-selected-class="text-bold"
            :rules="[required]"
            use-input
          >
            <template #option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="$t(`afaktoApp.DatastreamType.${scope.opt}_icon`)" size="xl" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ $t(`afaktoApp.DatastreamType.${scope.opt}`) }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-if="entity.type" #prepend>
              <q-icon :name="$t(`afaktoApp.DatastreamType.${entity.type}_icon`)" size="xl" />
            </template>
          </q-select>

          <q-select
            v-if="['BNP_ACCOUNT_STATEMENT', 'BNP_OUTSTANDING'].includes(entity.type)"
            v-model="entity.path"
            emit-value
            filled
            :label="$t('afaktoApp.datastream.path')"
            map-options
            :options="contracts"
            :option-label="
              contract =>
                contract ? `${contract.contractNumber} => ${contract.company?.name} / ${contract.financialInformation?.currency}` : ''
            "
            option-value="contractNumber"
          />
          <q-select
            v-if="entity.type == 'INVOICE'"
            v-model="entity.path"
            emit-value
            filled
            :label="$t('afaktoApp.datastream.path')"
            :options="companies"
            option-label="name"
            option-value="code"
          />
        </q-card-section>

        <q-card-actions v-if="entity.organization && entity.type">
          <q-uploader class="full-width" :label="$t('afaktoApp.datastream.upload.subtitle')" @added="added" />
        </q-card-actions>
      </q-card>
    </q-form>

    <q-card v-if="entity.type && DATASTREAM_HEADERS[entity.type]" style="margin: 2em auto 0; width: 60em">
      <q-card-section>
        <strong><q-icon name="help" /> {{ $t(`afaktoApp.datastream.upload.headers`) }}</strong>
        {{ $t('afaktoApp.datastream.upload.headers_help') }}
      </q-card-section>

      <q-card-section align="center" class="text-caption">
        {{ DATASTREAM_HEADERS[entity.type].join() }}
      </q-card-section>

      <q-card-section>
        <q-list>
          <q-item v-for="type in DATASTREAM_HEADERS[entity.type]" :key="type">
            <q-item-section>
              <q-item-label caption>{{ type }}</q-item-label>
              {{ $t(`afaktoApp.datastream.explanation.${type}`) }}
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>

    <q-inner-loading :showing="saving">
      <q-spinner-gears color="primary" size="10em" />
    </q-inner-loading>
  </q-page>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { api } from 'src/boot/axios';
import { required } from 'src/components/rules';
import { DATASTREAM_HEADERS } from 'src/constants/datastreamType';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import useNotifications from 'src/util/useNotifications.js';

const route = useRoute();

const baseApiUrl = '/api/datastreams';
const companies = ref([]);
const contracts = ref([]);
const entity = ref({ type: route.query.type });
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { notifyError } = useNotifications();
const organizations = ref([]);
const $q = useQuasar();
const router = useRouter();
const saving = ref(false);
const { t } = useI18n();

const fetchPaths = async () => {
  if (!entity.value.organization || !entity.value.type) return;

  // Default value
  entity.value.path = 'all';

  if (['BNP_ACCOUNT_STATEMENT', 'BNP_OUTSTANDING'].includes(entity.value.type)) {
    try {
      const response = await api.get('/api/contracts?sort=contractNumber');
      contracts.value = response.data.filter(contract => contract.company.orgId === entity.value.organization.orgId);
      entity.value.path = '';
    } catch (error) {
      notifyError(error);
      contracts.value = [];
    }
  } else if (entity.value.type === 'INVOICE') {
    try {
      const response = await api.get(`/api/companies?sort=name&orgId.equals=${entity.value.organization?.orgId}`);
      companies.value = response.data;
      entity.value.path = '';
    } catch (error) {
      notifyError(error);
      companies.value = [];
    }
  }
};

watch(() => [entity.value.organization, entity.value.type], fetchPaths);

const added = async files => {
  saving.value = true;
  const fileData = new FormData();

  // Append the file to the FormData instance
  for (const file of files) {
    fileData.append('file', file);
  }

  entity.value.orgId = entity.value.organization.orgId;

  // Convert datastream object to JSON string and append to FormData
  fileData.append('datastream', JSON.stringify(entity.value));

  try {
    const response = await api.post(baseApiUrl + '/upload', fileData);
    $q.notify({
      icon: 'check',
      color: 'positive',
      message: t('afaktoApp.datastream.created')
    });
    router.push({ path: response.data.id });
  } catch (error) {
    notifyError(error);
  }
  saving.value = false;
};

onMounted(async () => {
  if (hasRoleAdmin) organizations.value = (await api.get('/api/organizations')).data;
  else entity.value.organization = { orgId: useAuthenticationStore().account.orgId };
});
</script>
