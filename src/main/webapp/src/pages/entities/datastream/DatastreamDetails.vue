<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.datastream.detail.title') }}</h1>

      <q-space />

      <q-btn class="buttonNeutral" icon="download" @click="downloadFile(entity)">
        <q-tooltip>{{ $t(`afaktoApp.DatastreamType.${entity.type}`) }}</q-tooltip>
      </q-btn>
      <q-btn class="buttonNeutral" icon="close" @click="router.back()">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy>
      <q-card>
        <q-card-section v-if="entity.type">
          <q-input filled :label="$t('afaktoApp.datastream.type')" :model-value="$t(`afaktoApp.DatastreamType.${entity.type}`)" readonly>
            <template #prepend>
              <q-icon :name="$t(`afaktoApp.DatastreamType.${entity.type}_icon`)" size="xl" />
            </template>
          </q-input>

          <b-input v-model="entity.path" :label="$t('afaktoApp.datastream.path')" readonly />
          <b-input v-model="entity.name" :label="$t('afaktoApp.datastream.name')" readonly />
          <b-input v-model="entity.inserts" :label="$t('afaktoApp.datastream.inserts')" readonly type="number" />
          <b-input v-model="entity.updates" :label="$t('afaktoApp.datastream.updates')" readonly type="number" />
          <b-input v-model="entity.deletes" :label="$t('afaktoApp.datastream.deletes')" readonly type="number" />
        </q-card-section>
      </q-card>

      <q-card v-if="entity.error || entity.failuresCount">
        <q-banner v-if="entity.error" class="text-white bg-red">
          {{ entity.error }}
          <template #avatar><q-icon name="error" /></template>
        </q-banner>

        <q-table v-if="entity.failuresCount" :columns="columns" :rows="entity.failures" :title="$t('afaktoApp.datastream.failures')">
          <template #body="props">
            <q-tr :class="props.row.raw && 'cursor-pointer'" :props="props" @click="props.row.expand = !props.row.expand">
              <q-td align="right" auto-width>{{ $n(props.row.line) }}</q-td>
              <q-td>{{ props.row.message }}</q-td>
              <q-td auto-width>
                <q-btn v-if="props.row.raw" color="accent" dense :icon="props.row.expand ? 'expand_less' : 'expand_more'" round />
              </q-td>
            </q-tr>
            <q-tr v-show="props.row.raw && props.row.expand" :props="props">
              <q-td colspan="3">
                {{ props.row.raw }}
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card>
    </q-form>

    <entity-meta :entity="entity" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { exportFile } from 'quasar';
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import EntityMeta from 'pages/subcomponents/EntityMeta.vue';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/datastreams';
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();

const columns = computed(() => [
  {
    name: 'Line',
    label: 'Line',
    field: 'line',
    align: 'right'
  },
  {
    name: 'Reason',
    label: 'Reason',
    field: 'message',
    align: 'left',
    sortable: true
  },
  { align: 'right' }
]);

const entity = ref({
  address: { city: '' },
  contact: { name: '' },
  paymentTerms: {}
});

const downloadFile = async datastream => {
  try {
    const response = await api.get(`${baseApiUrl}/${datastream.id}/download`, {
      responseType: 'blob'
    });
    exportFile(datastream.name, response.data);
  } catch (error) {
    notifyError(error);
  }
};

onMounted(async () => {
  if (!route.params.id) return;

  try {
    entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
  } catch (error) {
    notifyError(error);
  }
});
</script>
