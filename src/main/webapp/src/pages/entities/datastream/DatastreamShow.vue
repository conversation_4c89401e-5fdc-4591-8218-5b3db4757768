<template>
  <q-drawer overlay show-if-above side="right">
    <header>
      <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>

      <q-item-section>
        <q-item-label class="show-label">
          <q-icon name="file_copy" />
          {{ modelValue.type.replace(/_/g, ' ') }}
        </q-item-label>
        <q-item-label>
          <h3>{{ modelValue.name }}</h3>
        </q-item-label>
        <q-item-label>
          <q-btn class="buttonNeutral" icon="o_file_download" :label="$t(`entity.action.download`)" @click="downloadFile(modelValue)" />
        </q-item-label>
      </q-item-section>
    </header>

    <q-list>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.datastream.path') }}</q-item-section>
        <q-item-section avatar>{{ modelValue.path }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.datastream.inserts') }}</q-item-section>
        <q-item-section avatar>{{ $n(modelValue.inserts) }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.datastream.updates') }}</q-item-section>
        <q-item-section avatar>{{ modelValue.updates }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.datastream.deletes') }}</q-item-section>
        <q-item-section avatar>{{ modelValue.deletes }}</q-item-section>
      </q-item>
    </q-list>

    <q-list v-if="modelValue.failuresCount">
      <q-expansion-item default-opened dense-toggle expand-separator :label="$t('afaktoApp.datastream.failures')">
        <q-table :columns="columns" :rows="modelValue.failures" virtual-scroll>
          <template #body="props">
            <q-tr :class="props.row.raw && 'cursor-pointer'" :props="props" @click="props.row.expand = !props.row.expand">
              <q-td align="right" auto-width>{{ $n(props.row.line) }}</q-td>
              <q-td>{{ props.row.message }}</q-td>
            </q-tr>
            <q-tr v-show="props.row.raw && props.row.expand" :props="props">
              <q-td colspan="2">
                {{ props.row.raw }}
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-expansion-item>
    </q-list>
  </q-drawer>
</template>

<script setup>
import { api } from 'boot/axios.js';
import { exportFile } from 'quasar';
import { computed } from 'vue';

import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/datastreams';
const { notifyError } = useNotifications();

defineEmits(['close']);

defineProps({ modelValue: { type: Object, required: true } });

const downloadFile = async modelValue => {
  try {
    const response = await api.get(`${baseApiUrl}/${modelValue.id}/download`, {
      responseType: 'blob'
    });
    exportFile(modelValue.name, response.data);
  } catch (error) {
    notifyError(error);
  }
};

const columns = computed(() => [
  {
    name: 'Line',
    label: 'Line',
    field: 'line',
    align: 'left'
  },
  {
    name: 'Reason',
    label: 'Reason',
    field: 'message',
    align: 'left'
  }
]);
</script>
