<template>
  <q-page>
    <q-table v-model:pagination="pagination" :rows="rows" :columns="columns" row-key="id" binary-state-sort @request="onRequest">
      <template #top-left>
        <b-title :model-value="$t('afaktoApp.externalCreditInsurance.home.title')" />
      </template>
      <template #top-right>
        <q-btn class="buttonBrand" icon="add" :label="$t('entity.action.add')" to="/external-credit-insurance/new" />
      </template>
      <template #body="props">
        <q-tr class="cursor-pointer" :props="props" @click="onRowSelect(props.row.id)">
          <q-td>
            {{ props.row.name }}
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';

const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();
const { t } = useI18n();

const rows = ref([]);

const pagination = ref({
  sortBy: route.query.sortBy || 'name',
  descending: route.query.descending === 'true',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const columns = computed(() => [
  {
    name: 'name',
    align: 'left',
    label: t('afaktoApp.externalCreditInsurance.name'),
    field: 'name',
    sortable: true
  }
]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get('/api/external-credit-insurances', {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`
    }
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

onMounted(() => onRequest({ pagination: pagination.value }));

const onRowSelect = id => router.push(`/external-credit-insurance/${id}/edit`);
</script>
