import { api } from 'boot/axios';

import type { Country } from '../buyer/buyer.service';

const baseApiUrl = '/api/contracts';

export interface Company {
  id: null | string;
  name: null | string;
}

export interface FactorInstitution {
  id: null | string;
  name: null | string;
}

export interface FinancialInformation {
  currency: null | string;
  guaranteeLine: null | number;
  nonGuaranteeLine: null | number;
  guaranteeFund: null | number;

  factoringCommission: null | number;
  minFactoringCommission: null | number;

  margin: null | number;
  indexRate: null | number;
  indexRateFloor: null | number;
}

export enum FactoringProgramType {
  LINE_BY_LINE = 'LINE_BY_LINE',
  BALANCE_BY_RELOADING = 'BALANCE_BY_RELOADING',
  BALANCE_BY_CANCELLING_AND_REPLACING = 'BALANCE_BY_CANCELLING_AND_REPLACING'
}

export interface CreditInsurancePolicy {
  externalCreditInsurance: null | string;
  buyerId: null | string;
  policyId: null | string;
}

export interface Contract {
  id: null | string;
  company: null | Company;
  factorInstitution: null | FactorInstitution;
  contractNumber: null | string;
  type: null | FactoringProgramType;
  factorAccountNumber: null | string;
  syndicatedProgram: null | boolean;
  confidentialProgram: null | boolean;
  withRecourse: null | boolean;
  unfunding: null | boolean;
  monthlyMaxCession: null | number;
  signatureDate: null | Date;
  activationDate: null | Date;
  countries: Country[]; // List of countries
  creditInsurancePolicy: null | CreditInsurancePolicy;
  financialInformation: null | FinancialInformation;
}

export interface IContractService {
  retrieve(): Promise<any>;
  get(id: string): Promise<Contract>;
  getList(page: number, size: number, sort: string): Promise<any>;
  save(contract: Contract): Promise<any>;
  delete(id: string): Promise<any>;
}

class ContractServiceImpl implements IContractService {
  public save(contract: Contract) {
    return api({
      method: contract.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: contract.id || '',
      data: contract
    });
  }

  public get(id: string): Promise<Contract> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(`${baseApiUrl}/${id}`)
        .then(res => {
          res.data.financialInformation = res.data.financialInformation || {};
          resolve(res.data);
        })
        .catch(err => {
          console.log(err);
          reject(err);
        });
    });
  }

  public delete(id: string) {
    return api.delete(`${baseApiUrl}/${id}`);
  }

  public getList(page: number, size: number, sort: string): Promise<Contract> {
    return api.get(baseApiUrl, {
      params: {
        page: page,
        size: size,
        sort: sort
      }
    });
  }

  public retrieve(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(baseApiUrl)
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  }
}

const ContractService = new ContractServiceImpl() as IContractService;

export default ContractService;
