<template>
  <q-drawer overlay show-if-above side="right">
    <header>
      <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>
        <q-item-section class="q-gutter-y-sm ">
            <q-item-label class="show-label">
              <q-icon name="edit_note" />
              <div>{{ $t('afaktoApp.contract.detail.title').toUpperCase() }}</div>
            </q-item-label>
            <q-item-label>
              <h3>{{ (modelValue?.company?.name).toUpperCase() }}</h3>
            </q-item-label>
            <q-item-label>
              <p class="no-margin text-neutralHigher"> {{ modelValue?.financialInformation?.currency }}</p>
            </q-item-label>
            <q-item-label>
              <q-btn icon="o_done_all" class="buttonNeutral"
                     @click="$router.push(`/cessions?company.in=${modelValue?.company?.id}`)"
                     :label="$t(`afaktoApp.cession.home.title`)" />
            </q-item-label>
        </q-item-section>
    </header>
      <q-list padding>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.contract.contractNumber') }}</q-item-section>
          <q-item-section avatar>{{ modelValue?.contractNumber }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> {{ $t('afaktoApp.contract.signatureDate') }}</q-item-section>
          <q-item-section avatar> {{ modelValue?.signatureDate }}
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.contract.activationDate') }}</q-item-section>
          <q-item-section avatar>{{ modelValue?.activationDate }}</q-item-section>
        </q-item>
      </q-list>

      <q-separator />
      <q-list>
        <q-expansion-item dense-toggle expand-separator
                          label="Contract details">
          <div class="q-pa-sm">
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.syndicatedProgram') }}</q-item-section>
              <q-item-section avatar> {{ modelValue?.syndicatedProgram ? 'Yes' : 'No' }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.confidentialProgram') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.confidentialProgram ? 'Yes' : 'No' }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.withRecourse') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.withRecourse ? 'Yes' : 'No' }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.programType') }}</q-item-section>
              <q-item-section avatar>
                {{ modelValue?.programType ? modelValue.programType.replaceAll('_', ' ').toLowerCase().charAt(0).toUpperCase()
                + modelValue.programType.replaceAll('_', ' ').toLowerCase().slice(1) : ''
                }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.monthlyMaxCession') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.monthlyMaxCession }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.defaultOverDue') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.defaultOverDue }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.country') }}</q-item-section>
              <q-item-section avatar>
                <template v-if="modelValue?.country">
                  <em :class="`fi fi-${modelValue.country.toLowerCase()}`" />
                  <q-tooltip>{{ countryNames.of(modelValue.country.toUpperCase()) }}</q-tooltip>
                </template>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.contract.countries') }}</q-item-section>
              <q-item-section avatar>
                <template v-if="modelValue?.countries && modelValue.countries.length > 0">
                  <div class="row q-gutter-xs">
                    <div v-for="country in modelValue.countries" :key="country">
                      <em :class="`fi fi-${country.toLowerCase()}`">
                        <q-tooltip>{{ countryNames.of(country.toUpperCase()) }}</q-tooltip>
                      </em>
                    </div>
                  </div>
                </template>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section> {{ $t('afaktoApp.contract.cashIn') }}</q-item-section>
              <q-item-section avatar> {{ modelValue?.cashIn ? 'Yes' : 'No' }}</q-item-section>
            </q-item>
          </div>
        </q-expansion-item>
      </q-list>

      <q-separator />

      <q-list>
        <q-expansion-item dense-toggle expand-separator
                          label="Factor details">
          <div class="q-pa-sm">
          <q-item>
            <q-item-section>
              {{ $t('afaktoApp.contract.factorInstitution.name') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.factorInstitution?.name }}
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section>
              {{ $t('afaktoApp.contract.factorAccountNumber') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.factorAccountNumber }}
            </q-item-section>
          </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.guaranteeLine') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.guaranteeLine != null
                ? $n(modelValue.financialInformation.guaranteeLine, 'currency', { currency: modelValue.financialInformation.currency })
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.nonGuaranteeLine') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.nonGuaranteeLine != null
                ? $n(modelValue.financialInformation.nonGuaranteeLine, 'currency', { currency: modelValue.financialInformation.currency })
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.guaranteeFund') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.guaranteeFund != null
                ? $n(modelValue.financialInformation.guaranteeFund, 'percent')
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.factoringCommission') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.factoringCommission != null
                ? $n(modelValue.financialInformation.factoringCommission %)
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.minFactoringCommission') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.minFactoringCommission != null
                ? $n(modelValue.financialInformation.minFactoringCommission, 'currency', { currency: modelValue.financialInformation.currency })
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.margin') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.margin != null
                ? $n(modelValue.financialInformation.margin, 'percent')
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.indexRate') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.indexRate != null
                ? $n(modelValue.financialInformation.indexRate, 'percent')
                : '-' }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                {{ $t('afaktoApp.contract.financialInformation.indexRateFloor') }}
              </q-item-section>
              <q-item-section avatar>
                {{ modelValue?.financialInformation?.indexRateFloor != null
                ? $n(modelValue.financialInformation.indexRateFloor, 'percent')
                : '-' }}
              </q-item-section>
            </q-item>
          </div>
        </q-expansion-item>
      </q-list>
      <q-separator />
      <q-list>
        <q-expansion-item dense-toggle expand-separator
                          label=" Insurer">
          <div class="q-pa-sm">
          <q-item>
            <q-item-section>
              {{ $t('afaktoApp.contract.creditInsurance.name') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.creditInsurance }}
            </q-item-section>
          </q-item>
            <div v-if="modelValue?.creditInsurancePolicy?.externalCreditInsurance">
              <q-item>
            <q-item-section>
              {{ $t('afaktoApp.contract.creditInsurance.name') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.creditInsurancePolicy?.externalCreditInsurance?.name }}
            </q-item-section>
          </q-item>
              <q-item v-if="modelValue?.creditInsurancePolicy?.customerId">
                <q-item-section>
                  {{ $t('afaktoApp.externalCreditInsurance.policy.customerId') }}
                </q-item-section>
                <q-item-section avatar>
                  {{ modelValue?.creditInsurancePolicy?.customerId }}
                </q-item-section>
              </q-item>
              <q-item>
            <q-item-section>
              {{ $t('afaktoApp.externalCreditInsurance.policy.policyId') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.creditInsurancePolicy?.policyId }}
            </q-item-section>
          </q-item>
              <q-item
                v-if="modelValue?.creditInsurancePolicy?.blindCoverAmount">
            <q-item-section>
              {{ $t('afaktoApp.externalCreditInsurance.policy.blindCoverAmount') }}
            </q-item-section>
            <q-item-section avatar>
              {{ modelValue?.creditInsurancePolicy?.blindCoverAmount }}
            </q-item-section>
          </q-item>
          <q-item v-else>
            <q-item-section>
              {{ $t('afaktoApp.externalCreditInsurance.policy.blindCover') }}
            </q-item-section>
            <q-item-section avatar>
              No
            </q-item-section>
          </q-item>
            </div>
          </div>
        </q-expansion-item>
      </q-list>

      <!-- Espace pour permettre le scroll jusqu'en bas, bidouille nécessaire -->
      <div style="height: 50px;"></div>

      <!--      <q-list>-->
      <!--        <q-expansion-item v-if="" dense-toggle expand-separator label="History & Comments">-->
      <!--          <div class="q-pa-sm">-->
      <!--            <div>{{ ?.history }}</div>-->
      <!--            <div>{{ ?.comments }}</div>-->
      <!--          </div>-->
      <!--        </q-expansion-item>-->

      <!--      </q-list>-->

      <!--      <entity-meta :entity="" />-->

    <footer>
      <q-btn @click="() => router.push(`/contracts/${modelValue.id}`)" icon="edit" class="buttonBrand" label="Edit">

      </q-btn>
    </footer>
  </q-drawer>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  modelValue: Object
});

const { locale } = useI18n();

const countryNames = new Intl.DisplayNames([navigator.language], { type: 'region' });

const monthOptions = computed(() =>
  [...Array(12).keys()].map(i => ({
    label: new Date(0, i).toLocaleString(locale.value, { month: 'long' }),
    value: i + 1
  }))
);

const getFormattedMonth = (monthNumber) => {
  if (!monthNumber) return '';
  const monthOption = monthOptions.value.find(option => option.value === monthNumber);
  return monthOption ? monthOption.label : monthNumber;
};


</script>
