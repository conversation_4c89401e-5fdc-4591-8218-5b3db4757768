<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.cession.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" v-if="hasRoleWriter" icon="add" :label="$t('afaktoApp.cession.home.createLabel')" to="/cessions/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      @request="onRequest"
      @row-click="(_, { id }) => router.push(`/cessions/${id}`)"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';

const baseApiUrl = '/api/cessions';
const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { n, t } = useI18n();
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.contract.factorInstitution.name,
    filter: {
      field: 'factorInstitution',
      optionLabel: 'name',
      optionValue: 'id',
      type: 'select',
      url: '/api/factor-institutions?sort=name'
    },
    label: t('afaktoApp.factorInstitution.detail.title'),
    name: 'contract.factorInstitution.name',
    sortable: true
  },
  {
    align: 'left',
    field: row => row.contract.company.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name }))
    },
    label: t('afaktoApp.company.detail.title'),
    name: 'contract.company.name',
    sortable: true
  },
  {
    align: 'center',
    field: 'createdDate',
    format: value => format(value, 'dd/MM/yyyy HH:mm'),
    label: t('global.field.createdDate'),
    name: 'createdDate',
    sortable: true
  },
  {
    field: 'count',
    filter: { type: 'number' },
    format: value => n(value),
    label: t('afaktoApp.cession.count'),
    name: 'count',
    sortable: true
  },
  {
    field: row => row.contract.financialInformation.currency,
    filter: { field: 'currency', type: 'currency' },
    label: t('afaktoApp.cession.currency'),
    name: 'contract.financialInformation.currency',
    sortable: true
  },
  {
    classes: props => (props.sum < 0 ? 'text-negative' : 'text-positive'),
    field: 'sum',
    filter: { type: 'number' },
    format: (value, row) => n(value, 'currency', { currency: row.contract.financialInformation.currency }),
    label: t('afaktoApp.cession.sum'),
    name: 'sum',
    sortable: true
  }
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value)
    }
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};
</script>
