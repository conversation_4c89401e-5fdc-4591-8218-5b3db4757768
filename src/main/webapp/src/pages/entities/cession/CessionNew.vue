<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.cession.home.title') }} | {{ $t('afaktoApp.cession.detail.title') }}</h1>

      <q-space />

      <q-btn
        v-if="contract"
        class="buttonBrand"
        :disabled="!contract.activationDate || contract.activationDate > date.formatDate(new Date(), 'YYYY-MM-DD')"
        icon="done_all"
        :label="$t('entity.action.submit') + ' ' + $t('afaktoApp.cession.detail.title')"
        :readonly="!contract.activationDate || contract.activationDate > date.formatDate(new Date(), 'YYYY-MM-DD')"
        type="submit"
        @click="onSubmit"
      >
        <q-tooltip v-if="contract.activationDate > date.formatDate(new Date(), 'YYYY-MM-DD')">
          <q-icon name="info" size="md" />
          {{ $t('afaktoApp.cession.noActivationDate') }}
        </q-tooltip>
      </q-btn>

      <q-btn class="buttonNeutral" icon="close" to=".">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-toolbar>
      <q-space />
      <q-select v-model="contract" borderless :options="contracts" @update:model-value="loadContract()">
        <template #selected>
          <q-item v-if="contract">
            <q-item-section>{{ contract.company?.name }}</q-item-section>
            <q-item-section avatar>({{ contract.financialInformation.currency }})</q-item-section>
          </q-item>
          <q-item v-else>
            <q-item-section>{{ $t('afaktoApp.cession.detail.selectContract') }}</q-item-section>
          </q-item>
        </template>
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section>{{ scope.opt.company.name }}</q-item-section>
            <q-item-section avatar>{{ scope.opt.financialInformation.currency }}</q-item-section>
          </q-item>
        </template>
      </q-select>
      <q-space />
    </q-toolbar>

    <section v-if="contract" class="row justify-center q-gutter-md q-pa-md">
      <q-card class="situation-card">
        <q-card-section>
          <div class="card-header">{{ $t('afaktoApp.cession.count') }}</div>
          <h2 class="text-right">{{ $n(abstract.reduce((sum, type) => sum + type.INSERT.count + type.UPDATE.count, 0)) }}</h2>
          <change-number-display :value="abstract.reduce((sum, type) => sum + type.INSERT.count - type.DELETE.count, 0)" />
        </q-card-section>

        <q-inner-loading :showing="loadingToSell">
          <q-spinner-gears color="primary" size="lg" />
        </q-inner-loading>
      </q-card>

      <q-card class="situation-card">
        <q-card-section>
          <div class="float-right">{{ contract.financialInformation.currency }}</div>
          <div class="card-header">{{ $t('afaktoApp.cession.sum') }}</div>
          <h2 class="text-right">
            {{
              $n(
                abstract.reduce((sum, type) => sum + type.INSERT.sum + type.UPDATE.sum, 0),
                'currency',
                { currency: contract.financialInformation.currency }
              )
            }}
          </h2>
          <change-number-display
            :value="abstract.reduce((sum, type) => sum + type.INSERT.sum - type.DELETE.sum, 0)"
            format="currency"
            :currency="contract.financialInformation.currency"
          />
        </q-card-section>

        <q-inner-loading :showing="loadingToSell">
          <q-spinner-gears color="primary" size="lg" />
        </q-inner-loading>
      </q-card>

      <q-card v-if="contract.cashIn" class="situation-card">
        <q-card-section>
          <div class="card-header">
            <q-icon color="warning" name="bolt" size="xs" />
            {{ $t('afaktoApp.cession.gaps') }}
            -
            {{ $t('afaktoApp.invoice.gapCredit_values.false') }}
            <q-icon
              class="cursor-pointer float-right"
              clickable
              name="east"
              @click="$router.push(`/invoices?context=_gapDebitor&futureGapForContract.equals=${contract.id}`)"
            />
          </div>
          <h2 class="text-right">{{ $n(abstract.reduce((sum, type) => sum + type.gapDebitor.count, 0)) }}</h2>
          <h2 class="text-right">
            {{
              $n(
                abstract.reduce((sum, type) => sum + type.gapDebitor.sum, 0),
                'currencyCode',
                { currency: contract.financialInformation.currency }
              )
            }}
          </h2>
        </q-card-section>
      </q-card>

      <q-card v-if="contract.cashIn" class="situation-card">
        <q-card-section>
          <div class="card-header">
            <q-icon color="warning" name="bolt" size="xs" />
            {{ $t('afaktoApp.cession.gaps') }}
            -
            {{ $t('afaktoApp.invoice.gapCredit_values.true') }}
            <q-icon
              class="cursor-pointer float-right"
              clickable
              name="east"
              @click="
                $router.push(
                  `/invoices?context=_gapCreditor&company.in=${contract.company.id}&currency.equals=${contract.financialInformation.currency}&balance.equals=0&isUnderFactor.equals=false&reconciliationJournal.equals=${contract.journal}&gapCession.specified=false`
                )
              "
            />
          </div>
          <h2 class="text-right">{{ $n(abstract.reduce((sum, type) => sum + type.gapCreditor.count, 0)) }}</h2>
          <h2 class="text-right">
            {{
              $n(
                abstract.reduce((sum, type) => sum + type.gapCreditor.sum, 0),
                'currencyCode',
                { currency: contract.financialInformation.currency }
              )
            }}
          </h2>
        </q-card-section>
      </q-card>
    </section>

    <section v-if="contract" class="row justify-center q-pa-md">
      <q-card>
        <!-- Display an abstract of amounts for the selected, paid and current invoices -->
        <q-table :columns="abstractColumns" hide-pagination row-key="type" :rows="abstract">
          <template #header>
            <q-tr>
              <q-th></q-th>
              <q-th colspan="3">
                <q-icon color="positive" left name="add_circle" size="xs" />
                {{ $t('afaktoApp.cession.sold') }}
              </q-th>
              <q-th colspan="3">
                <q-icon color="info" left name="pending" size="xs" />
                {{ $t('afaktoApp.cession.current') }}
              </q-th>
              <q-th colspan="3">
                <q-icon color="negative" left name="do_not_disturb_on" size="xs" />
                {{ $t('afaktoApp.cession.paid') }}
              </q-th>
              <q-th v-if="contract.cashIn" colspan="3">
                <q-icon color="warning" name="bolt" size="xs" />
                {{ $t('afaktoApp.cession.gaps') }}
                -
                {{ $t('afaktoApp.invoice.gapCredit_values.false') }}
              </q-th>
              <q-th v-if="contract.cashIn" colspan="3">
                <q-icon color="warning" name="bolt" size="xs" />
                {{ $t('afaktoApp.cession.gaps') }}
                -
                {{ $t('afaktoApp.invoice.gapCredit_values.true') }}
              </q-th>
            </q-tr>
          </template>
        </q-table>
      </q-card>
    </section>

    <q-form v-if="contract" class="column q-pt-md" greedy @submit="onSubmit">
      <q-tabs v-model="tab" class="q-px-md" breakpoint="2000" inline-label>
        <q-tab name="toSell">
          <q-icon color="positive" left name="add_circle" size="md" />
          {{ $t('afaktoApp.cession.toSell') }}
          <q-badge floating>{{ toSell?.length }}</q-badge>
        </q-tab>
        <q-tab name="current">
          <q-icon color="info" left name="pending" size="md" />
          {{ $t('afaktoApp.cession.current') }}
          <q-badge floating>{{ paginationCurrent.rowsNumber }}</q-badge>
        </q-tab>
        <q-tab name="paid">
          <q-icon color="negative" left name="do_not_disturb_on" size="md" />
          <template v-if="overdueBefore">
            {{ $t('afaktoApp.cession.paidOrOverdueBefore', { date: format(overdueBefore) }) }}
          </template>
          <template v-else>
            {{ $t('afaktoApp.cession.paidOrExcluded') }}
          </template>
          <q-badge floating>{{ paginationPaid.rowsNumber }}</q-badge>
        </q-tab>
      </q-tabs>

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="toSell">
          <q-table
            v-model:pagination="pagination"
            v-model:selected="toSell"
            binary-state-sort
            class="cursor-pointer"
            :columns="columns.filter(column => !['gap', 'reconciliationJournal'].includes(column.field))"
            hide-pagination
            hide-selected-banner
            :loading="loadingToSell"
            row-key="id"
            :rows="sellable"
            selection="multiple"
            style="height: 40em"
            virtual-scroll
            :rows-per-page-options="[0]"
            @row-click="onRowSelect"
          />
        </q-tab-panel>

        <q-tab-panel name="current">
          <q-table
            v-model:pagination="paginationCurrent"
            binary-state-sort
            :columns="columns.filter(column => !['gap', 'reconciliationJournal'].includes(column.field))"
            row-key="id"
            :rows="current"
            :rows-per-page-options="[0]"
            style="height: 40em"
            @request="loadCurrent"
          />
        </q-tab-panel>

        <q-tab-panel name="paid">
          <q-table
            v-model:pagination="paginationPaid"
            binary-state-sort
            :columns="columns"
            row-key="id"
            :rows="paid"
            :rows-per-page-options="[0]"
            style="height: 40em"
            @request="loadPaid"
          >
            <template #body-cell-gap="props">
              <q-td :props="props">
                <template v-if="!contract.cashIn" />
                <template v-else-if="props.row.balance == 0 && !props.row.reconciliationJournal" />
                <template v-else-if="props.row.balance == 0 && props.row.reconciliationJournal == contract.journal" />
                <template v-else>
                  <q-icon color="warning" name="bolt" size="md" />
                  <template v-if="props.row.balance == 0">{{ $t('afaktoApp.invoice.gapType_values.RECONCILIATION') }}</template>
                  <template v-else-if="new Date(props.row.dueDate) < overdueBefore">
                    {{ $t('afaktoApp.invoice.gapType_values.OVERDUE') }}
                  </template>
                  <template v-else-if="props.row.buyer.excluded || props.row.excluded">{{
                    $t('afaktoApp.invoice.gapType_values.EXCLUSION')
                  }}</template>
                </template>
              </q-td>
            </template>
          </q-table>
        </q-tab-panel>
      </q-tab-panels>
    </q-form>

    <q-inner-loading :showing="saving">
      <q-spinner-gears color="primary" size="10em" />
    </q-inner-loading>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { date, useQuasar } from 'quasar';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { InvoiceType } from 'src/shared/model/enumerations/invoice-type.model';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications.js';

const $q = useQuasar();
const { n, t } = useI18n();
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();

const contracts = ref([]);
const contract = ref();
const overdueBefore = computed(() => {
  if (!contract.value?.defaultOverDue) return;
  const date = new Date();
  date.setDate(date.getDate() - contract.value.defaultOverDue);
  const activationDate = new Date(contract.value.activationDate);
  return date < activationDate ? activationDate : date;
});

const loadContracts = async () => {
  contracts.value = (await api.get(`/api/contracts?sort=company.name&sort=contractNumber`)).data;

  // Retrieve the contract from the URL
  const contractId = route.query.contract;
  if (contractId) {
    contract.value = contracts.value.find(c => c.id === contractId);
    loadContract();
  }
};

const loadContract = async () => {
  // Keeps the contract in the URL
  router.replace({ query: { contract: contract.value.id } });
  loadSellable({ pagination: pagination.value });
  loadCurrent({ pagination: paginationCurrent.value });
  loadPaid({ pagination: paginationPaid.value });

  loadAbstract();
};

const abstractSellable = ref([]);
const abstractCurrent = ref([]);
const abstractPaid = ref([]);
const abstractGapsDebitor = ref([]);
const abstractGapsCreditor = ref([]);

const loadAbstract = async () => {
  const responseSellable = await api.get('/api/invoices/byType', {
    params: {
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': false,
      'balance.notEquals': 0,
      'buyerExcluded.equals': false,
      'excluded.equals': false,
      'hasCover.equals': true,
      'dueDate.greaterThanOrEqual': new Date().toISOString()?.split('T')[0]
    }
  });
  abstractSellable.value = responseSellable.data;

  const responseCurrent = await api.get('/api/invoices/byType', {
    params: {
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': true,
      'balance.notEquals': 0,
      'buyerExcluded.equals': false,
      'excluded.equals': false,
      ...(overdueBefore.value && {
        'dueDate.greaterThanOrEqual': overdueBefore.value.toISOString().split('T')[0]
      })
    }
  });
  abstractCurrent.value = responseCurrent.data;

  const responsePaid = await api.get('/api/invoices/byType', {
    params: {
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': true,
      ...(overdueBefore.value
        ? { 'paidOrExcludedOrDueDate.lessThan': overdueBefore.value.toISOString().split('T')[0] }
        : { 'paidOrExcluded.equals': true })
    }
  });
  abstractPaid.value = responsePaid.data;

  const responseGapsDebitor = await api.get('/api/invoices/byType', {
    params: {
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': true,
      'futureGapForContract.equals': contract.value.id
    }
  });
  abstractGapsDebitor.value = responseGapsDebitor.data;

  const responseGapsCreditor = await api.get('/api/invoices/byType', {
    params: {
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': false,
      'reconciliationJournal.equals': contract.value.journal,
      'gapType.specified': false
    }
  });
  abstractGapsCreditor.value = responseGapsCreditor.data;
};

// The pre-calculated amounts and numbers of sellable, current and paid receivables
const abstract = computed(() =>
  Object.values(InvoiceType).map(type => {
    return {
      type,
      INSERT: {
        count: toSell.value.filter(i => i.type === type).length,
        sum: toSell.value.filter(i => i.type === type).reduce((acc, i) => acc + i.balance, 0)
      },
      UPDATE: {
        count: abstractCurrent.value.find(i => i.type === type)?.count || 0,
        sum: abstractCurrent.value.find(i => i.type === type)?.totalBalance || 0
      },
      DELETE: {
        count: abstractPaid.value.find(i => i.type === type)?.count || 0,
        sum: abstractPaid.value.find(i => i.type === type)?.totalAmount || 0
      },
      gapDebitor: {
        count: abstractGapsDebitor.value.find(gap => gap.type === type)?.count || 0,
        sum: abstractGapsDebitor.value.find(gap => gap.type === type)?.totalAmount || 0
      },
      gapCreditor: {
        count: abstractGapsCreditor.value.find(gap => gap.type === type)?.count || 0,
        sum: abstractGapsCreditor.value.find(gap => gap.type === type)?.totalAmount || 0
      }
    };
  })
);

const abstractColumns = computed(() => [
  { align: 'left', field: 'type', format: v => t(`afaktoApp.InvoiceType.${v}`) },
  { classes: 'table-separator' },
  { field: row => toSell.value.filter(i => i.type === row.type).length, format: v => n(v) },
  {
    field: row => toSell.value.filter(i => i.type === row.type).reduce((acc, i) => acc + i.balance, 0),
    format: v => n(v, 'currencyCode', { currency: contract.value.financialInformation.currency })
  },
  { classes: 'table-separator' },
  { field: row => abstractCurrent.value.find(i => i.type === row.type)?.count || 0, format: v => n(v) },
  {
    field: row => abstractCurrent.value.find(i => i.type === row.type)?.totalBalance || 0,
    format: v => n(v, 'currencyCode', { currency: contract.value.financialInformation.currency })
  },
  { classes: 'table-separator' },
  { field: row => abstractPaid.value.find(i => i.type === row.type)?.count || 0, format: v => n(v) },
  {
    field: row => abstractPaid.value.find(i => i.type === row.type)?.totalAmount || 0,
    format: v => n(v, 'currencyCode', { currency: contract.value.financialInformation.currency })
  },
  { classes: 'table-separator' },
  // Four more columns for gaps count and amount
  {
    classes: () => (contract.value.cashIn ? '' : 'hidden'),
    field: row => abstractGapsDebitor.value.find(gap => gap.type === row.type)?.count || 0,
    format: v => n(v)
  },
  {
    classes: () => (contract.value.cashIn ? '' : 'hidden'),
    field: row => abstractGapsDebitor.value.find(gap => gap.type === row.type)?.totalAmount || 0,
    format: v => n(v, 'currencyCode', { currency: contract.value.financialInformation.currency })
  },
  {
    classes: () => (contract.value.cashIn ? '' : 'hidden'),
    field: row => abstractGapsCreditor.value.find(gap => gap.type === row.type)?.count || 0,
    format: v => n(v)
  },
  {
    classes: () => (contract.value.cashIn ? '' : 'hidden'),
    field: row => abstractGapsCreditor.value.find(gap => gap.type === row.type)?.totalAmount || 0,
    format: v => n(v, 'currencyCode', { currency: contract.value.financialInformation.currency })
  }
]);

const tab = ref(route.query.tab || 'toSell');

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.buyer?.name,
    label: t('afaktoApp.invoice.buyer'),
    name: 'buyer.name',
    sortable: true
  },
  {
    field: 'type',
    format: v => t(`afaktoApp.InvoiceType.${v}`),
    label: t('afaktoApp.invoice.type'),
    name: 'type',
    sortable: true
  },
  {
    align: 'left',
    field: 'invoiceNumber',
    label: t('afaktoApp.invoice.invoiceNumber'),
    name: 'invoiceNumber',
    sortable: true
  },
  {
    field: 'date',
    format: value => format(value),
    label: t('afaktoApp.invoice.date'),
    name: 'date',
    sortable: true
  },
  {
    field: 'dueDate',
    format: value => format(value),
    label: t('afaktoApp.invoice.dueDate'),
    name: 'dueDate',
    sortable: true
  },
  {
    classes: props => (props.amount < 0 ? 'text-negative' : 'text-positive'),
    field: 'amount',
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.invoice.amount'),
    name: 'amount',
    sortable: true
  },
  {
    classes: props => (props.balance < 0 ? 'text-negative' : 'text-positive'),
    field: 'balance',
    format: (value, row) => n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.invoice.balance'),
    name: 'balance',
    sortable: true
  },
  {
    classes: contract.value.journal == null ? 'hidden' : '',
    field: 'reconciliationJournal',
    label: t('afaktoApp.invoice.reconciliationJournal'),
    headerClasses: contract.value.journal == null ? 'hidden' : '',
    name: 'reconciliationJournal',
    sortable: true
  },
  {
    classes: contract.value.cashIn ? '' : 'hidden',
    field: 'gap',
    headerClasses: contract.value.cashIn ? '' : 'hidden',
    label: t('afaktoApp.invoice.gap'),
    name: 'gap'
  }
]);

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false'
});

const loadingToSell = ref(false);
const sellable = ref({});
const toSell = ref([]);
const current = ref({});
const paid = ref({});

const loadSellable = async props => {
  const { sortBy, descending } = props.pagination;
  loadingToSell.value = true;
  sellable.value = [];
  toSell.value = [];

  const response = await api.get('/api/invoices', {
    params: {
      size: 10000,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': false,
      'balance.notEquals': 0,
      'buyerExcluded.equals': false,
      'excluded.equals': false,
      'hasCover.equals': true,
      'dueDate.greaterThanOrEqual': new Date().toISOString()?.split('T')[0]
    }
  });
  sellable.value = response.data;
  sellable.value.rowsNumber = response.headers['x-total-count'];
  toSell.value = response.data;
  loadingToSell.value = false;
};

const paginationCurrent = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: 1,
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const loadCurrent = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  current.value = [];

  const response = await api.get('/api/invoices', {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? paginationCurrent.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': true,
      'balance.notEquals': 0,
      'buyerExcluded.equals': false,
      'excluded.equals': false,
      ...(overdueBefore.value && {
        'dueDate.greaterThanOrEqual': overdueBefore.value.toISOString().split('T')[0]
      })
    }
  });
  current.value = response.data;

  paginationCurrent.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
};

const paginationPaid = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: 1,
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const loadPaid = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  paid.value = [];

  const response = await api.get('/api/invoices', {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? paginationPaid.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      'company.equals': contract.value.company.id,
      'currency.equals': contract.value.financialInformation.currency,
      'isUnderFactor.equals': true,
      ...(overdueBefore.value
        ? { 'paidOrExcludedOrDueDate.lessThan': overdueBefore.value.toISOString().split('T')[0] }
        : { 'paidOrExcluded.equals': true })
    }
  });
  paid.value = response.data;

  paginationPaid.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
};

const saving = ref(false);

const onRowSelect = async (_, row) => {
  if (toSell.value.includes(row)) {
    toSell.value = toSell.value.filter(i => i !== row);
  } else {
    toSell.value.push(row);
  }
};

const onSubmit = async () =>
  $q
    .dialog({
      title: t('afaktoApp.cession.confirm.title'),
      message: t('afaktoApp.cession.confirm.message'),
      cancel: true
    })
    .onOk(createCession);

const createCession = async () => {
  saving.value = true;

  // Create a map to store unique buyer objects
  const buyerMap = new Map();
  // Replace each buyer with a reference to the unique buyer object
  toSell.value.forEach(invoice => {
    invoice.buyer.company = invoice.buyer.company.id;
    if (!buyerMap.has(invoice.buyer.id)) {
      buyerMap.set(invoice.buyer.id, invoice.buyer);
    } else {
      invoice.buyer = buyerMap.get(invoice.buyer.id).id; // Replace buyer object with its ID
    }
  });

  try {
    await api.post('/api/cessions', {
      contract: contract.value,
      invoices: toSell.value
    });
    $q.notify({
      type: 'positive',
      message: t('afaktoApp.cession.created', { param: contract.value.company.name }),
      icon: 'done'
    });
    router.push({ path: '/cessions' });
  } catch (error) {
    notifyError(error);
  } finally {
    saving.value = false;
  }
};

onMounted(() => loadContracts());
</script>
