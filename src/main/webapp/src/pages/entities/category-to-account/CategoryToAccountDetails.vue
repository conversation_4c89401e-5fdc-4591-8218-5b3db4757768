<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.categoryToAccount.detail.title') }}</h1>

      <q-space />

      <q-btn v-if="hasRoleConfig && entity.id" color="secondary" icon="delete" :label="$t('entity.action.delete')" @click="onDelete" />
      <q-btn class="buttonNeutral" icon="close" to="/bank-transactions">
        <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
      </q-btn>
    </q-toolbar>

    <q-form greedy @submit="onSubmit">
      <q-card>
        <q-card-section>
          <q-select
            v-model="entity.contract"
            class="required"
            filled
            :label="$t('afaktoApp.contract.detail.title')"
            :options="contracts"
            :option-label="contract => contract.company.name + ' - ' + contract.financialInformation?.currency"
            :rules="[required]"
            @update:model-value="changeContract"
          />

          <q-list v-if="entity.contract" separator>
            <q-item
              v-for="categoryToAccount in BANK_TRANSACTION_CATEGORY"
              :key="categoryToAccount"
              :active="entity.category === categoryToAccount"
              clickable
              dense
              @click="changeCategory(categoryToAccount)"
            >
              <q-item-section>
                <q-item-label>{{ capitalize(categoryToAccount.replaceAll('_', ' ').toLowerCase()) }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label>{{ getCompanyMapping(categoryToAccount)?.ledgerAccount }}</q-item-label>
              </q-item-section>

              <sup>{{ getNumberOfExtraAccounts(categoryToAccount) }}</sup>

              <q-icon v-if="getCompanyMapping(categoryToAccount)?.comment" class="absolute" name="info" style="right: 0" />
              <q-tooltip v-if="getCompanyMapping(categoryToAccount)?.comment">
                {{ getCompanyMapping(categoryToAccount)?.comment }}
              </q-tooltip>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>

      <q-card v-if="entity.category">
        <q-card-section class="row">
          <b-input v-model="entity.ledgerAccount" autofocus class="col" :label="$t('afaktoApp.categoryToAccount.ledgerAccount')" />
          <b-input v-model="entity.bank" class="col" :label="$t('afaktoApp.categoryToAccount.bank')" />
        </q-card-section>
        <q-card-section class="row">
          <b-input v-model="entity.ledgerHolding" class="col" :label="$t('afaktoApp.categoryToAccount.ledgerHolding')" />
          <b-input v-model="entity.bankHolding" class="col" :label="$t('afaktoApp.categoryToAccount.bankHolding')" />
        </q-card-section>

        <q-card-section>
          <div class="text-h6">{{ $t('afaktoApp.categoryToAccount.reinvoicing') }}</div>
        </q-card-section>

        <q-card-section class="row">
          <b-input v-model="entity.ledgerReinvoicing" class="col" :label="$t('afaktoApp.categoryToAccount.ledgerReinvoicing')" />
          <b-input v-model="entity.bankReinvoicing" class="col" :label="$t('afaktoApp.categoryToAccount.bankReinvoicing')" />
        </q-card-section>
        <q-card-section class="row">
          <b-input
            v-model="entity.ledgerReinvoicingHolding"
            class="col"
            :label="$t('afaktoApp.categoryToAccount.ledgerReinvoicingHolding')"
          />
          <b-input v-model="entity.bankReinvoicingHolding" class="col" :label="$t('afaktoApp.categoryToAccount.bankReinvoicingHolding')" />
        </q-card-section>

        <q-card-section>
          <q-input v-model="entity.comment" filled :label="$t('afaktoApp.categoryToAccount.comment')">
            <template #prepend><q-icon name="info" /></template>
          </q-input>
        </q-card-section>

        <q-card-actions v-if="hasRoleConfig" align="center">
          <q-toggle v-model="keepCreating" style="position: absolute; left: 1em">
            <q-icon name="compost" size="md" />
            <q-tooltip>Type the ledger account, then "ENTER" and you will automatically move to the next category</q-tooltip>
          </q-toggle>
          <q-btn class="buttonBrand" icon="label_important" :label="$t('entity.action.save')" type="submit" />
        </q-card-actions>

        <div v-if="entity?.id" class="absolute-bottom">
          <entity-meta-dates :entity="entity" />
        </div>
      </q-card>
    </q-form>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { format, useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import EntityMetaDates from 'pages/subcomponents/EntityMetaDates.vue';
import { required } from 'src/components/rules';
import { BANK_TRANSACTION_CATEGORY } from 'src/constants/bankTransaction';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/category-to-accounts';
const { capitalize } = format;
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();

const companyMappings = ref([]);
const contracts = ref([]);
const entity = ref({});

const loadCompanyMappings = async contract => {
  try {
    companyMappings.value = (
      await api.get(baseApiUrl, {
        params: {
          'company.equals': contract.company.id,
          'currency.equals': contract.financialInformation?.currency,
          sort: 'category'
        }
      })
    ).data;
  } catch (error) {
    notifyError(error);
  }
};

const changeContract = async contract => {
  loadCompanyMappings(contract);
  entity.value = { contract, company: contract.company, currency: contract.financialInformation.currency };
};

const changeCategory = async category => {
  entity.value.category = category;
  const mapping = companyMappings.value.find(
    companyMapping => companyMapping.category == category && companyMapping.currency == entity.value.currency
  );
  document.querySelector('input[type=text]')?.focus();

  const contract = entity.value.contract;
  entity.value = {
    category,
    contract,
    company: contract.company,
    currency: contract.financialInformation.currency,
    ...mapping
  };
};

const getCompanyMapping = category =>
  companyMappings.value.find(companyMapping => companyMapping.category == category && companyMapping.currency == entity.value.currency);

const getNumberOfExtraAccounts = category => {
  const otherAccounts = [
    'bank',
    'ledgerHolding',
    'bankHolding',
    'ledgerReinvoicing',
    'bankReinvoicing',
    'ledgerReinvoicingHolding',
    'bankReinvoicingHolding'
  ];

  const count = otherAccounts.reduce((acc, account) => acc + (getCompanyMapping(category)?.[account] ? 1 : 0), 0);

  return count > 1 ? `(+${count})` : '';
};

onMounted(async () => {
  contracts.value = (await api.get('/api/contracts?sort=company.name&sort=financialInformation.currency')).data;

  if (route.params?.id) {
    try {
      entity.value = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
      entity.value.contract = contracts.value.find(
        contract => contract.company.id === entity.value.company.id && contract.financialInformation.currency === entity.value.currency
      );
      loadCompanyMappings(entity.value.contract);
    } catch (error) {
      notifyError(error);
    }
  }

  setupBeforeUnload(t, document.forms[0], entity.value);
});

const keepCreating = ref(true);

const onSubmit = async () => {
  try {
    if (entity.value.ledgerAccount) {
      await api({
        method: entity.value.id ? 'put' : 'post',
        baseURL: baseApiUrl,
        url: entity.value.id,
        data: entity.value
      });
      $q.notify({
        icon: 'check',
        color: 'positive',
        message: t(`afaktoApp.categoryToAccount.${entity.value.id ? 'updated' : 'created'}`)
      });
    } else if (entity.value.id) {
      await api.delete(`${baseApiUrl}/${entity.value.id}`);
      $q.notify({
        icon: 'announcement',
        color: 'warning',
        message: t('afaktoApp.categoryToAccount.deleted')
      });
    }

    // To go to the next category
    const nextCategoryIndex = BANK_TRANSACTION_CATEGORY.indexOf(entity.value.category) + 1;
    if (keepCreating.value && nextCategoryIndex < BANK_TRANSACTION_CATEGORY.length) {
      loadCompanyMappings(entity.value.contract);
      changeCategory(BANK_TRANSACTION_CATEGORY[nextCategoryIndex]);
      // Focus on the ledger account input field
      document.querySelector('input[type=text]')?.focus();
    } else {
      router.back();
    }
  } catch (error) {
    notifyError(error);
  }
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.categoryToAccount.delete.question', { id: entity.value.name }),
    cancel: true
  }).onOk(() => {
    api
      .delete(`${baseApiUrl}/${entity.value.id}`)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement'
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
