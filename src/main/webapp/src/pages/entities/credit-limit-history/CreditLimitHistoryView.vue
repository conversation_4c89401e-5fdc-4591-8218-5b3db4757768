<template>
  <q-page class="q-pa-md flex flex-center">
    <div class="responsive-width q-gutter-md">
      <q-field :label="$t('afaktoApp.creditLimitHistory.startDate')" readonly stack-label>
        <template #control>
          {{ creditLimitHistory.data.startDate }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimitHistory.endDate')" readonly stack-label>
        <template #control>
          {{ creditLimitHistory.data.endDate }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimitHistory.creditLimit')" readonly stack-label>
        <template #control>
          {{ creditLimitHistory.data.creditLimit && creditLimitHistory.data.creditLimit.id }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimitHistory.buyer')" readonly stack-label>
        <template #control>
          {{ creditLimitHistory.data.buyer && creditLimitHistory.data.buyer.id }}
        </template>
      </q-field>
      <div class="flex justify-end">
        <router-link v-slot="{ navigate }" :to="`/credit-limit-histories/${creditLimitHistory.data.id}/edit`" custom>
          <q-btn icon="edit" color="primary" @click="navigate" />
        </router-link>
      </div>
    </div>
  </q-page>
</template>

<script>
import { api } from 'boot/axios';
import { defineComponent, reactive } from 'vue';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'PageCreditLimitHistoryView',

  setup() {
    const route = useRoute();

    const creditLimitHistory = reactive({
      data: {
        id: null,
        startDate: null,
        endDate: null,
        creditLimit: null,
        buyer: null
      }
    });

    (async function fetchCreditLimitHistory() {
      if (route.params.id) {
        creditLimitHistory.data = (await api.get(`/api/credit-limit-histories/${route.params.id}`)).data;
      }
    })();

    return {
      creditLimitHistory
    };
  }
});
</script>
