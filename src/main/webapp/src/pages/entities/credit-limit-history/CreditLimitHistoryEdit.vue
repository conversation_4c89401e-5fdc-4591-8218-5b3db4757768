<template>
  <q-page class="q-pa-md flex flex-center">
    <b-title :model-value="$t('afaktoApp.creditLimitHistory.detail.title')" />
    <q-form class="responsive-width q-gutter-md" greedy @submit="onSubmit">
      <q-input v-model="creditLimitHistory.data.startDate" :label="$t('afaktoApp.creditLimitHistory.startDate')" @keydown.enter.prevent />
      <q-input v-model="creditLimitHistory.data.endDate" :label="$t('afaktoApp.creditLimitHistory.endDate')" @keydown.enter.prevent />
      <q-select
        v-model="creditLimitHistory.data.creditLimit"
        :label="$t('afaktoApp.creditLimitHistory.creditLimit')"
        :options="[null].concat(creditLimits)"
        option-label="id"
        :rules="[$rules.required()]"
      />
      <q-select
        v-model="creditLimitHistory.data.buyer"
        :label="$t('afaktoApp.creditLimitHistory.buyer')"
        :options="[null].concat(buyers)"
        option-label="id"
      />
      <div class="flex justify-between">
        <q-btn type="submit" color="primary" :label="$t('entity.action.save')" :loading="loading" :disable="loading" />
      </div>
    </q-form>
  </q-page>
</template>

<script>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { defineComponent, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

export default defineComponent({
  name: 'PageCreditLimitHistoryEdit',

  setup() {
    const baseApiUrl = '/api/credit-limit-histories';
    const { t } = useI18n();
    const $q = useQuasar();
    const router = useRouter();
    const route = useRoute();

    const creditLimitHistory = reactive({
      data: {
        id: null,
        startDate: null,
        endDate: null,
        creditLimit: null,
        buyer: null
      }
    });

    const loading = ref(false);

    (async function fetchCreditLimitHistory() {
      if (route.params.id) {
        creditLimitHistory.data = (await api.get(`${baseApiUrl}/${route.params.id}`)).data;
      }
    })();

    const creditLimits = ref([]);
    (async function fetchCreditLimits() {
      creditLimits.value = (await api.get(`/api/creditLimits`)).data;
    })();

    const buyers = ref([]);
    (async function fetchClients() {
      buyers.value = (await api.get(`/api/buyers`)).data;
    })();

    const onSubmit = async () => {
      loading.value = true;
      try {
        await api({
          method: creditLimitHistory.data.id ? 'put' : 'post',
          baseURL: baseApiUrl,
          url: creditLimitHistory.data.id,
          data: creditLimitHistory.data
        });
        router.back();
      } catch (e) {
        loading.value = false;
        if (e.response.status !== 400) return;
        $q.notify({
          type: 'negative',
          message: t(e.response.data.message)
        });
      }
    };

    return {
      creditLimitHistory,
      creditLimits,
      buyers,
      onSubmit,
      loading
    };
  }
});
</script>
