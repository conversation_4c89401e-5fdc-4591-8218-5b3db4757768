import { api } from 'boot/axios';

const baseApiUrl = '/api/bank-transactions';

export interface Company {
  id: null | string;
  name: null | string;
}

export enum BankTransactionType {
  DEBIT = 'DEBIT',
  CREDIT = 'CREDIT'
}
export interface BankTransaction {
  id: null | string;
  company: null | Company;
  date: null | Date;
  transactionReferenceNumber: null | string;
  type: null | BankTransactionType;
  value: null | number;
  narrative: null | string;
  identificationCode: null | boolean;
  referenceAccountServiceInstitution: null | boolean;
}

export interface IBankTransactionService {
  retrieve(page: number, size: number, sort: string): Promise<any>;
  get(id: string): Promise<BankTransaction>;
  save(bankTransaction: BankTransaction): Promise<any>;
  delete(id: string): Promise<any>;
}

class BankTransactionServiceImpl implements IBankTransactionService {
  public save(bankTransaction: BankTransaction) {
    return api({
      method: bankTransaction.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: bankTransaction.id || '',
      data: bankTransaction
    });
  }

  public get(id: string): Promise<BankTransaction> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(`${baseApiUrl}/${id}`)
        .then(res => {
          resolve(res.data);
        })
        .catch(err => {
          console.log(err);
          reject(err);
        });
    });
  }

  public delete(id: string) {
    return api.delete(`${baseApiUrl}/${id}`);
  }

  public getList(page: number, size: number, sort: string): Promise<BankTransaction> {
    return api.get(baseApiUrl, {
      params: {
        page: page,
        size: size,
        sort: sort
      }
    });
  }

  public retrieve(page: number, size: number, sort: string): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(baseApiUrl, {
          headers: { 'content-type': 'text/plain' },
          params: {
            page: page,
            size: size,
            sort: sort
          }
        })
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  }
}

const BankTransactionService = new BankTransactionServiceImpl() as IBankTransactionService;

export default BankTransactionService;
