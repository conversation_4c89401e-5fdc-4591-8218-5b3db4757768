<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.bankTransaction.home.title') }} | {{ $t('afaktoApp.bankTransaction.with-offset.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <q-btn
          v-if="hasRoleConfig"
          class="buttonNeutral"
          icon="format_list_numbered_rtl"
          :label="$t('entity.action.configure')"
          to="/category-to-accounts/new"
        />

        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" icon="close" :label="$t('afaktoApp.bankTransaction.with-offset.title')" to="bank-transactions">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :visible-columns="visibleColumns"
      @request="onRequest"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/bank-transactions/with-offset';
const hasRoleConfig = useAuthenticationStore().hasRoleConfig;
const { n, t } = useI18n();
const { notifyError } = useNotifications();
const route = useRoute();
const router = useRouter();

const columns = computed(() => [
  {
    align: 'left',
    field: row => row.company.name,
    filter: {
      field: 'company',
      type: 'enum',
      values: useAuthenticationStore().account.companies.map(company => ({ value: company.id, label: company.name }))
    },
    label: t('afaktoApp.bankTransaction.company.name'),
    name: 'company.name',
    sortable: true
  },
  {
    align: 'left',
    field: 'date',
    filter: { type: 'date' },
    format: value => format(value),
    label: t('afaktoApp.bankTransaction.date'),
    name: 'date',
    sortable: true
  },
  {
    align: 'left',
    field: 'currency',
    filter: { type: 'currency' },
    label: t('afaktoApp.bankTransaction.currency'),
    name: 'currency',
    sortable: true
  },
  {
    field: row => (row.amount > 0 ? row.amount : ''),
    filter: false,
    format: (value, row) => value && n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.bankTransaction.type_DEBIT'),
    name: 'debit'
  },
  {
    field: row => (row.amount < 0 ? -row.amount : ''),
    filter: false,
    format: (value, row) => value && n(value, 'currency', { currency: row.currency }),
    label: t('afaktoApp.bankTransaction.type_CREDIT'),
    name: 'credit'
  },
  {
    align: 'left',
    field: 'ledgerAccount',
    filter: { type: 'text' },
    label: t('afaktoApp.bankTransaction.ledgerAccount'),
    name: 'ledgerAccount',
    sortable: true
  },
  {
    align: 'left',
    field: 'narrative',
    filter: { type: 'text' },
    label: t('afaktoApp.bankTransaction.narrative'),
    name: 'narrative',
    sortable: true
  }
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'date',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 15,
  rowsNumber: 15
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...filtersToQueryParams(filters.value),
        'ledgerAccount.specified': true
      }
    });
    rows.value = response.data;

    pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
    router.replace({ query: { page, sortBy, descending, rowsPerPage } });
  } catch (err) {
    notifyError(err);
  }
};
</script>
