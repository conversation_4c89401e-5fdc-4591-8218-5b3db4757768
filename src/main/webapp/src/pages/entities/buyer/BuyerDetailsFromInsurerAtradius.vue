<template>
  <q-item v-if="modelValue.totalDecision.decisionAmtInPolicyCurrency == 0">
    <q-item-section>
      <h1 class="text-red-7 text-center">REFUSED</h1>
    </q-item-section>
  </q-item>

  <q-item v-else-if="modelValue.creditLimitApplicationAmountInPolicyCurrency < modelValue.totalDecision.decisionAmtInPolicyCurrency">
    <q-item-section>
      <h1 class="text-orange-7 text-center">PARTIALLY APPROVED</h1>
    </q-item-section>
  </q-item>

  <q-item v-else-if="modelValue.creditLimitApplicationAmountInPolicyCurrency == modelValue.totalDecision.decisionAmtInPolicyCurrency">
    <q-item-section>
      <h1 class="text-green-7 text-center">APPROVED</h1>
    </q-item-section>
  </q-item>

  <b-input
    input-class="text-right"
    label="Requested Amount"
    :model-value="
      $n(parseInt(modelValue.creditLimitApplicationAmountInPolicyCurrency), 'currencyCode', {
        currency: modelValue.policyCurrencyCode
      })
    "
    readonly
  />

  <b-input
    v-if="modelValue.totalDecision"
    input-class="text-right"
    label="Granted Amount"
    :model-value="
      $n(parseInt(modelValue.totalDecision.decisionAmtInPolicyCurrency), 'currencyCode', {
        currency: modelValue.policyCurrencyCode
      })
    "
    readonly
  />

  <q-input :model-value="format(modelValue.effectFromDate, 'yyyy-MM-dd')" filled label="Effect from date" readonly type="date" />
  <q-input :model-value="format(modelValue.decisionDate, 'yyyy-MM-dd')" filled label="Decision date" readonly type="date" />
  <q-input :model-value="format(modelValue.applicationDate, 'yyyy-MM-dd')" filled label="Application date" readonly type="date" />
  <q-input
    :model-value="format(new Date(modelValue.secondAmtDecision.decisionExpiryDate), 'yyyy-MM-dd')"
    filled
    label="End date"
    readonly
    type="date"
  />

  <q-input
    v-for="condition of modelValue.firstAmtDecision.decisionConditions"
    :model-value="condition.conditionDescription"
    filled
    label="First amount decision"
    readonly
    rows="3"
    type="textarea"
  />

  <q-input
    v-for="condition of modelValue.secondAmtDecision.decisionConditions"
    :model-value="condition.conditionDescription"
    filled
    label="Second amount decision"
    readonly
    rows="3"
    type="textarea"
  />

  <q-input
    v-if="modelValue.totalDecision && modelValue.totalDecision.decisionAmtInPolicyCurrency !== 0"
    v-for="condition of modelValue.totalDecision.decisionConditions"
    :model-value="condition.conditionDescription"
    filled
    label="Total decision"
    readonly
    rows="3"
    type="textarea"
  />

  <q-input
    v-if="modelValue.totalDecision.decisionAmtInPolicyCurrency == 0"
    v-for="condition of modelValue.totalDecision.decisionConditions"
    :model-value="condition.conditionDescription"
    filled
    label="Refusal Reason"
    readonly
    rows="3"
    type="textarea"
  />

  <q-input
    v-if="modelValue.totalDecision.decisionAmtInPolicyCurrency == 0"
    v-for="condition of modelValue.totalDecision.decisionConditions"
    :model-value="condition.conditionCategoryDescription"
    filled
    label="Refusal Category"
    readonly
    rows="3"
    type="textarea"
  />

  <q-input
    v-if="modelValue.withdrawalDate"
    :model-value="format(modelValue.withdrawalDate, 'yyyy-MM-dd\'T\'HH:mm')"
    filled
    label="Withdrawal date"
    readonly
    type="datetime-local"
  />
</template>

<script setup>
import { format } from 'src/util/format';

defineModel({ type: Object });
</script>
