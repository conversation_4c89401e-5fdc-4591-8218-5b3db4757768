import { api } from 'boot/axios';

const baseApiUrl = '/api/buyers';

export interface Search {
  name: string;
  country: Country;
  number: string;
  numberType: string;
  insurerCode: string;
}

export interface Buyer {
  id: string;
  code: string;
  numberType: string;
  number: null | string;
  name: string;
  address: Address;
  contact: Contact;
  paymentTerms: PaymentTerms;
  buyerFromInsurer: null | BuyerFromInsurer;
  buyerFromFactor: null | BuyerFromFactor;
}

export interface Country {
  alpha2Code: null | string;
  alpha3Code: null | string;
  name: null | string;
}

export interface Address {
  streetName: null | string;
  streetNumber: null | string;
  postalCode: null | string;
  city: null | string;
  stateProvince: null | string;
  country: null | Country;
}

export interface Contact {
  name: null | string;
  phone: null | string;
  email: null | string;
}

export interface PaymentTerms {
  numberOfDays: null | number;
}

export interface BuyerFromInsurer {
  insurerCode: null | string;
}

export interface BuyerFromFactor {
  number: null | string;
  name: null | string;
  code: null | string;
  factorCode: null | string;
  currency: null | string;
  amountApproved: null | number;
  amountOutstanding: null | number;
  amountDraftReceived: null | number;
  amountFunded: null | number;
  amountSecured: null | number;
}

export interface IBuyerService {
  retrieve(): Promise<any>;

  get(id: string): Promise<Buyer>;

  getList(page: number, size: number, sort: string): Promise<any>;

  save(buyer: Buyer): Promise<any>;

  delete(id: string): Promise<any>;

  searchByBuyer(buyer: Buyer): Promise<any>;

  search(search: Search): Promise<any>;

  contract(id: string): Promise<any>;

  getInsurerDecision(buyerId: string, deliveryId: string): Promise<any>;
}

class BuyerServiceImpl implements IBuyerService {
  public save(buyer: Buyer) {
    return api({
      method: buyer.id ? 'put' : 'post',
      baseURL: baseApiUrl,
      url: buyer.id || '',
      data: buyer
    });
  }

  public get(id: string): Promise<Buyer> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(`${baseApiUrl}/${id}`)
        .then(res => {
          // Ensure entity.contact is never null or undefined
          res.data.address = res.data.address || { city: '' };
          res.data.contact = res.data.contact || { name: '' };
          res.data.paymentTerms = res.data.paymentTerms || {};
          resolve(res.data);
        })
        .catch(err => {
          console.log(err);
          reject(err);
        });
    });
  }

  public delete(id: string) {
    return api.delete(`${baseApiUrl}/${id}`);
  }

  public getList(page: number, size: number, sort: string): Promise<Buyer> {
    return api.get(baseApiUrl, {
      params: {
        page: page,
        size: size,
        sort: sort
      }
    });
  }

  public retrieve(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      api
        .get(baseApiUrl)
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  }

  public contract(id: string): Promise<any> {
    return api.get(`${baseApiUrl}/${id}/contract`);
  }

  public search(search: Search): Promise<any> {
    return api.post(`${baseApiUrl}/search`, search);
  }

  public searchByBuyer(buyer: Buyer): Promise<any> {
    return this.search({
      country: buyer?.address?.country,
      name: buyer?.name,
      number: buyer?.number,
      numberType: buyer?.numberType,
      insurerCode: buyer?.buyerFromInsurer?.insurerCode
    });
  }

  public getInsurerDecision(buyerId: string, deliveryId: string): Promise<any> {
    return api.get(`${baseApiUrl}/${buyerId}/insurer-decision/${deliveryId}`);
  }
}

const BuyerService = new BuyerServiceImpl() as IBuyerService;

export default BuyerService;
