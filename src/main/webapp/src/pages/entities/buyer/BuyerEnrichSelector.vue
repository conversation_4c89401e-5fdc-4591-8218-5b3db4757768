<template>
  <div class="row items-center justify-around q-gutter-md q-mb-sm">
    <div>{{ filterMethod(modelValue, postalCodeFilter).length }} companies found</div>
    <q-input v-model="postalCodeFilter" debounce="300" dense filled :label="$t('afaktoApp.address.postalCodeFilter')" />
  </div>

  <q-table
    :columns="[]"
    :filter="postalCodeFilter"
    :filter-method="filterMethod"
    :rows="modelValue"
    :rows-per-page-options="[5, 10, 0]"
    hide-header
    row-key="number"
  >
    <template #body="props">
      <q-tr>
        <q-td class="no-padding">
          <q-card
            bordered
            class="q-py-sm q-px-md bg-brandLowest bg-brandMedium full-width"
            style="cursor: pointer; border-radius: 0 !important"
            @click="$emit('selected-entity', props.row)"
          >
            <div class="text-neutralHighest q-mb-sm text-weight-bolder" style="font-size: 18px">
              {{ props.row.name }}
            </div>
            <div class="text-neutralHigher">
              <div class="flex items-center">
                <q-badge class="q-mr-xs" color="blue">
                  {{ $t(`afaktoApp.NumberType.${props.row.address.country.toUpperCase()}.${props.row.numberType}.label`) }}
                  <q-tooltip>
                    {{ $t(`afaktoApp.NumberType.${props.row.numberType}`).replaceAll('_', ' ') }}
                  </q-tooltip>
                </q-badge>
                {{ props.row.number }}
              </div>
            </div>
            <div class="text-neutralHigher">
              <div class="flex items-center no-wrap ellipsis">
                <q-icon class="q-mr-xs" name="home" size="xs" />
                {{ props.row.address?.streetName }}, {{ props.row.address?.city }},
                {{ props.row.address?.postalCode }}
              </div>
            </div>
          </q-card>
        </q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script setup>
import { ref } from 'vue';

defineEmits(['selected-entity']);

defineModel({ type: Array, required: true });

const postalCodeFilter = ref('');

const filterMethod = (rows, terms) => {
  if (!terms) return rows;
  return rows.filter(row => row.address?.postalCode?.toString()?.toLowerCase()?.includes(terms.toLowerCase()));
};
</script>
