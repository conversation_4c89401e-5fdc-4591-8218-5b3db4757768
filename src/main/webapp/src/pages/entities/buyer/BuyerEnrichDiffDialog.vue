<template>
  <q-dialog :model-value="modelValue" @update:model-value="val => $emit('update:modelValue', val)">
    <q-card style="min-width: 350px">
      <q-toolbar>
        <div class="text-h6">{{ t('afaktoApp.buyer.enrichedChangesTitle') }}</div>
        <q-space />
        <q-btn class="buttonNeutral" icon="close" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-card-section v-if="loading" align="center">
        <q-spinner-dots color="primary" size="xl" />
      </q-card-section>

      <q-card-section v-else-if="countryDialog">
        <q-select
          v-model="search.country"
          :label="$t('afaktoApp.address.country')"
          :options="countryOptions"
          :rules="[required]"
          emit-value
          filled
          hide-bottom-space
          map-options
          option-label="name"
          option-value="code"
          @update:model-value="onCountrySelected"
        >
          <template v-if="search.country" #prepend>
            <span :class="`fi fi-${search.country}`"></span>
          </template>
          <template #option="scope">
            <q-item dense v-bind="scope.itemProps">
              <q-item-section side>
                <span :class="`fi fi-${scope.opt.code}`" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </q-card-section>

      <q-card-section v-else-if="selectEntityDialog">
        <buyer-enrich-selector :model-value="legalEntities" @selected-entity="selectEntity" />
      </q-card-section>

      <template v-else-if="enrichedBuyer">
        <q-card-section>
          <div
            v-if="
              props.entity.address !== enrichedBuyer.address ||
              props.entity.numberType !== enrichedBuyer.numberType ||
              props.entity.number !== enrichedBuyer.number
            "
          >
            <div
              v-if="props.entity.numberType !== enrichedBuyer.numberType || props.entity.number !== enrichedBuyer.number"
              class="row justify-around items-center q-gutter-md q-mb-md"
            >
              <div class="col">
                <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichPreviousNumber') }}</div>
                <div class="text-body1">{{ props.entity.number }}</div>
                <q-badge
                  v-if="
                    props.entity.address?.country &&
                    tm(`afaktoApp.NumberType.${props.entity.address.country.toUpperCase()}.${props.entity.numberType}`)?.label
                  "
                  color="blue"
                  >{{ t(`afaktoApp.NumberType.${enrichedBuyer.address.country.toUpperCase()}.${search.numberType}.label`) }}
                  <q-tooltip>
                    {{ $t(`afaktoApp.NumberType.${enrichedBuyer.numberType}`).replaceAll('_', ' ') }}
                  </q-tooltip>
                </q-badge>
                <q-badge v-else color="blue">{{ $t(`afaktoApp.NumberType.${props.entity.numberType}`).replaceAll('_', ' ') }} </q-badge>
              </div>

              <div class="col-auto flex items-center">
                <q-icon class="text-grey-7" name="arrow_forward" size="md" />
              </div>
              <div class="col">
                <div class="text-caption text-grey">{{ t('afaktoApp.buyer.enrichNewNumber') }}</div>
                <div class="text-body1">{{ enrichedBuyer.number }}</div>
                <q-badge color="blue"
                  >{{ t(`afaktoApp.NumberType.${enrichedBuyer.address.country.toUpperCase()}.${enrichedBuyer.numberType}.label`) }}
                  <q-tooltip>
                    {{ $t(`afaktoApp.NumberType.${enrichedBuyer.numberType}`).replaceAll('_', ' ') }}
                  </q-tooltip>
                </q-badge>
              </div>
            </div>

            <div class="row justify-around items-stretch q-gutter-md">
              <div v-if="props.entity.address" class="col">
                <address-comp :model-value="props.entity.address" :readonly="true" />
              </div>

              <div v-if="props.entity.address" class="col-auto flex items-center">
                <q-icon class="text-grey-7" name="arrow_forward" size="xl" />
              </div>

              <div class="col">
                <address-comp :model-value="enrichedBuyer.address" :readonly="false" />
              </div>
            </div>
          </div>
          <div v-else class="text-center text-grey">
            {{ t('afaktoApp.buyer.noChanges') }}
          </div>
        </q-card-section>

        <q-card-actions align="center">
          <q-btn
            :label="$t('entity.action.save')"
            class="buttonBrand"
            color="primary"
            icon="label_important"
            type="submit"
            @click="onSubmit(entity)"
          />
        </q-card-actions>
      </template>
    </q-card>
  </q-dialog>
</template>

<script setup>
import countries from 'flag-icons/country.json';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { required } from 'components/rules/index.js';
import BuyerEnrichSelector from 'pages/entities/buyer/BuyerEnrichSelector.vue';
import BuyerService from 'pages/entities/buyer/buyer.service';
import AddressComp from 'pages/subcomponents/AddressComp.vue';
import { findIdType } from 'src/util/findIdType.js';
import useNotifications from 'src/util/useNotifications';

const i18n = useI18n();
const loading = ref(false);
const { notifyError } = useNotifications();
const { t, tm } = i18n;

const closeDialog = () => emit('update:modelValue', false);
const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: { type: Boolean, required: true },
  entity: { type: Object, required: true }
});

const search = ref({
  country: props.entity.address?.country,
  name: props.entity.name,
  number: props.entity.number,
  numberType: props.entity.numberType
});

const countryDialog = ref(false);
const countryOptions = ref(countries.filter(c => c.iso));
const selectEntityDialog = ref(false);
const legalEntities = ref([]);
const enrichedBuyer = ref();

onMounted(async () => {
  if (await testOnLegalName()) return;

  if (await testOnLegalNumber()) return;

  notifyError(t(`afaktoApp.buyer.enrichmentErrors.NOT_FOUND`));

  closeDialog();
});

async function testOnLegalName() {
  if (hasLegalNumber()) return false;

  if (search.value.country) {
    await searchLegalEntity();
    return true;
  }
  countryDialog.value = true;
  return true;
}

const countryMapping = ref({});

async function testOnLegalNumber() {
  const { identifierType, countryFilter, evaluateIdentifier } = findIdType(i18n);
  await evaluateIdentifier(props.entity.number);

  const countryKeys = Object.keys(countryFilter.value);

  if (identifierType.value.country) {
    search.value.country = identifierType.value.country;
    search.value.numberType = identifierType.value.type;
    await searchLegalEntity();
    return true;
  } else if (countryKeys.length > 1) {
    countryMapping.value = countryFilter.value;
    countryOptions.value = countries.filter(c => countryKeys.includes(c.code));
    countryDialog.value = true;
    return true;
  }

  return false;
}

async function onCountrySelected() {
  countryDialog.value = false;
  if (hasLegalNumber()) search.value.numberType = countryMapping.value[search.value.country].type;
  await searchLegalEntity();
}

async function searchLegalEntity() {
  loading.value = true;

  try {
    const legalEntitiesSearch = await BuyerService.search(search.value);
    if (!legalEntitiesSearch.data.buyers.length) {
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.NOT_FOUND`));
      return closeDialog();
    }

    if (legalEntitiesSearch.data.buyers.length === 1) selectEntity(legalEntitiesSearch.data.buyers[0]);
    else if (legalEntitiesSearch.data.buyers.length > 1) {
      selectEntityDialog.value = true;
      legalEntities.value = legalEntitiesSearch.data.buyers;
    }

    loading.value = false;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else notifyError(err);
    closeDialog();
  }
}

const hasLegalNumber = () => props.entity?.number?.length > 3;

const overrideLegalEntityNumber = () => {
  if (hasLegalNumber()) return;
  props.entity.number = enrichedBuyer.value.number;
  props.entity.numberType = enrichedBuyer.value.numberType;
};

function selectEntity(selectedEntity) {
  selectEntityDialog.value = false;

  enrichedBuyer.value = {
    address: selectedEntity.address || {},
    numberType: selectedEntity.numberType,
    number: selectedEntity.number
  };
}

const onSubmit = async entity => {
  entity.address = enrichedBuyer.value.address;
  overrideLegalEntityNumber(enrichedBuyer.value);

  try {
    BuyerService.save(entity);
  } catch (error) {
    notifyError(error);
  } finally {
    closeDialog();
  }
};
</script>
