<template>
  <div class="row q-gutter-sm">
    <div v-for="col in columns" :key="col.name" class="col text-center">
      <div>
        <strong>{{ col.label }}</strong>
        <q-tooltip v-if="col.tooltip">{{ col.tooltip }}</q-tooltip>
      </div>
      <img
        v-if="col.name === 'creditInsurancePolicy.externalCreditInsurance.name'"
        :alt="col.field"
        :src="`/insurers/${col.field}.png`"
        style="height: 1.5rem"
      />
      <template v-else>
        {{ col.format ? col.format(col.field) : col.field }}
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { n, t } = useI18n();

const columns = computed(() =>
  [
    {
      field: insurerName.value,
      label: t('afaktoApp.externalCreditInsurance.home.title'),
      name: 'creditInsurancePolicy.externalCreditInsurance.name',
      sortable: true
    },
    {
      field: props.modelValue.creditInsurancePolicy?.policyId,
      label: t('afaktoApp.externalCreditInsurance.policy.policyId'),
      name: 'creditInsurancePolicy.policyId'
    },
    {
      field: props.entity?.insurerCode,
      label: t('afaktoApp.externalCreditInsurance.detail.insurerCodeTitle'),
      name: 'buyerFromInsurer.insurerCode',
      tooltip: insurerName?.value ? t(`afaktoApp.externalCreditInsurance.detail.insurerCode.${insurerName.value}`) : null
    },
    {
      field: props.modelValue.creditInsurancePolicy?.blindCoverAmount,
      format: value =>
        value == null ? '' : n(value, 'currencyCode', { currency: props.modelValue.financialInformation?.currency || 'EUR' }),
      label: t('afaktoApp.externalCreditInsurance.policy.blindCoverAmount'),
      name: 'creditInsurancePolicy.blindCoverAmount'
    }
  ].filter(col => col.field != null)
);

const insurerName = computed(() => props.modelValue.creditInsurancePolicy?.externalCreditInsurance?.name?.toUpperCase());

const props = defineProps({
  modelValue: { type: Object, required: true }, // contract
  entity: Object // buyerFromInsurer
});
</script>
