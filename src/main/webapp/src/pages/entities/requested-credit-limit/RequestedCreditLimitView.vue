<template>
  <q-page class="q-pa-md flex flex-center">
    <div class="responsive-width q-gutter-md">
      <q-field :label="$t('afaktoApp.creditLimit.totalOutstanding')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.totalOutstanding }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimit.outstandingUnderFactor')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.outstandingUnderFactor }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimit.currentCreditLimit')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.currentCreditLimit }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimit.requestedCreditLimit')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.requestedCreditLimit }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimit.date')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.date }}
        </template>
      </q-field>
      <q-field :label="$t('afaktoApp.creditLimit.status')" readonly stack-label>
        <template #control>
          {{ creditLimit.data.status }}
        </template>
      </q-field>
      <div class="flex justify-end">
        <router-link v-slot="{ navigate }" :to="`/credit-limits/${creditLimit.data.id}/edit`" custom>
          <q-btn icon="edit" color="primary" @click="navigate" />
        </router-link>
      </div>
    </div>
  </q-page>
</template>

<script>
import { api } from 'boot/axios';
import { defineComponent, reactive } from 'vue';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'PageCreditLimitRequestView',

  setup() {
    const route = useRoute();

    const creditLimit = reactive({
      data: {
        id: null,
        totalOutstanding: null,
        outstandingUnderFactor: null,
        currentCreditLimit: null,
        creditLimitRequest: null,
        date: null,
        status: null
      }
    });

    (async function fetchCreditLimit() {
      if (route.params.id) {
        creditLimit.data = (await api.get(`/api/buyers/${props.buyerId}/credit-limit-requests/${route.params.id}`)).data;
      }
    })();

    return {
      creditLimit
    };
  }
});
</script>
