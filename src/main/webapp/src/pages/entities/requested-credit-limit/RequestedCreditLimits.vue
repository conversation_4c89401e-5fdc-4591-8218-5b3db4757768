<template>
  <div class="q-pa-md">
    <q-table
      v-model:pagination="pagination"
      :rows="rows"
      :columns="columns"
      row-key="id"
      :loading="loading"
      binary-state-sort
      @request="onRequest"
    >
      <template #top-left>
        <b-title :model-value="$t('afaktoApp.creditLimit.home.title')" />
      </template>
      <template #top-right>
        <q-btn color="primary" icon="add" :label="$t('entity.action.add')" to="/credit-limit-requests/new" />
      </template>
      <template #body="props">
        <q-tr>
          <q-td>
            {{ props.row.id }}
          </q-td>
          <q-td>
            {{ props.row.totalOutstanding }}
          </q-td>
          <q-td>
            {{ props.row.outstandingUnderFactor }}
          </q-td>
          <q-td>
            {{ props.row.currentCreditLimit }}
          </q-td>
          <q-td>
            {{ props.row.requestedCreditLimit }}
          </q-td>
          <q-td>
            {{ props.row.date }}
          </q-td>
          <q-td>
            {{ props.row.status }}
          </q-td>
          <q-td>
            <router-link v-slot="{ navigate }" :to="`/credit-limit-requests/${props.row.id}`" custom>
              <q-btn icon="visibility" @click="navigate" />
            </router-link>
            <router-link v-slot="{ navigate }" :to="`/credit-limit-requests/${props.row.id}/edit`" custom>
              <q-btn icon="edit" @click="navigate" />
            </router-link>
            <q-btn icon="delete_forever" @click="deleteCreditLimit(props.row.id)" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>

<script>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { computed, defineComponent, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';

export default defineComponent({
  name: 'PageCreditLimitRequests',

  setup() {
    const $q = useQuasar();
    const { t } = useI18n();
    const store = useAuthenticationStore();
    const route = useRoute();
    const router = useRouter();

    const rows = ref([]);
    const loading = ref(false);

    const pagination = ref({
      sortBy: route.query.sortBy || 'id',
      descending: route.query.descending === 'true',
      page: Number.parseInt(route.query.page || 1),
      rowsPerPage: Number.parseInt(route.query.rowsPerPage) || 15,
      rowsNumber: 15
    });

    const columns = computed(() => [
      {
        name: 'id',
        align: 'left',
        label: t('afaktoApp.creditLimit.id'),
        field: 'id',
        sortable: true
      },
      {
        name: 'totalOutstanding',
        align: 'left',
        label: t('afaktoApp.creditLimit.totalOutstanding'),
        field: 'totalOutstanding',
        sortable: true
      },
      {
        name: 'outstandingUnderFactor',
        align: 'left',
        label: t('afaktoApp.creditLimit.outstandingUnderFactor'),
        field: 'outstandingUnderFactor',
        sortable: true
      },
      {
        name: 'currentCreditLimit',
        align: 'left',
        label: t('afaktoApp.creditLimit.currentCreditLimit'),
        field: 'currentCreditLimit',
        sortable: true
      },
      {
        name: 'requestedCreditLimit',
        align: 'left',
        label: t('afaktoApp.creditLimit.requestedCreditLimit'),
        field: 'requestedCreditLimit',
        sortable: true
      },
      {
        name: 'date',
        align: 'left',
        label: t('afaktoApp.creditLimit.date'),
        field: 'date',
        sortable: true
      },
      {
        name: 'status',
        align: 'left',
        label: t('afaktoApp.creditLimit.status'),
        field: 'status',
        sortable: true
      }
    ]);

    const onRequest = async props => {
      const { page, rowsPerPage, sortBy, descending } = props.pagination;

      loading.value = true;

      try {
        const response = await api.get('/api/buyers/${props.buyerId}/credit-limit-requests', {
          params: {
            page: page - 1,
            size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
            sort: `${sortBy},${descending ? 'desc' : 'asc'}`
          }
        });
        pagination.value.rowsNumber = response.headers['x-total-count'];
        rows.value = response.data;
      } finally {
        loading.value = false;
      }

      pagination.value.page = page;
      pagination.value.rowsPerPage = rowsPerPage;
      pagination.value.sortBy = sortBy;
      pagination.value.descending = descending;

      router.replace({ query: { page, sortBy, descending, rowsPerPage } });
    };

    onMounted(() => onRequest({ pagination: pagination.value }));

    return {
      store,
      loading,
      pagination,
      columns,
      rows,
      onRequest,
      deleteCreditLimit: id => {
        $q.dialog({
          message: t('afaktoApp.creditLimit.delete.question', { id: id }),
          cancel: true
        }).onOk(() => {
          api.delete(`/api/buyers/${props.buyerId}/credit-limit-requests/${id}`).then(() => {
            onRequest({ pagination: pagination.value });
          });
        });
      }
    };
  }
});
</script>
