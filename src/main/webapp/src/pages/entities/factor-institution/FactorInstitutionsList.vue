<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.factorInstitution.home.title') }}</h1>

      <q-space />

      <q-btn class="buttonBrand" icon="add" :label="$t('afaktoApp.factorInstitution.home.createLabel')" to="/factor-institutions/new" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      @request="onRequest"
      @row-click="(_event, { id }) => router.push(`/factor-institutions/${id}/edit`)"
    />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import { useAuthenticationStore } from 'src/stores/authentication-store';

const baseApiUrl = '/api/factor-institutions';
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();
const { t } = useI18n();

const rows = ref([]);

const pagination = ref({
  sortBy: route.query.sortBy || 'name',
  descending: route.query.descending === 'true',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const columns = computed(() => [
  {
    align: 'left',
    field: 'code',
    label: t('afaktoApp.factorInstitution.code'),
    name: 'code',
    sortable: true
  },
  {
    align: 'left',
    field: 'name',
    label: t('afaktoApp.factorInstitution.name'),
    name: 'name',
    sortable: true
  },
  {
    align: 'left',
    field: 'type',
    label: t('afaktoApp.factorInstitution.type'),
    name: 'type',
    sortable: true
  }
]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`
    }
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

onMounted(() => onRequest({ pagination: pagination.value }));
</script>
