<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.user.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />
        <div class="btn-separator" />
        <!--q-btn icon="o_upload" :label="$t('entity.action.import')" to="/users/upload" /-->
        <!--q-btn icon="o_upload" :label="$t('entity.action.import')" to="/users/upload" /-->
        <q-btn class="buttonBrand" icon="add" :label="$t('afaktoApp.user.home.createLabel')" to="/users/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      v-model:selected="selectedRows"
      @request="onRequest"
      @row-click="(_event, row) => (selectedRows = [row])"
    >
      <template #body-cell-orgId="props">
        <q-td v-if="hasRoleAdmin" align="left">
          <q-avatar class="on-left" size="36px">
            <img v-if="props.value?.logoUrl" :alt="props.value?.displayName" :src="props.value?.logoUrl" />
          </q-avatar>
          {{ props.value?.displayName }}
        </q-td>
      </template>
      <template #body-cell-login="props">
        <q-td align="left">
          <q-avatar class="on-left" size="36px">
            <img v-if="props.row.imageUrl" :src="props.row.imageUrl" />
          </q-avatar>
          {{ props.value }}
        </q-td>
      </template>
      <template #body-cell-activated="props">
        <q-td :props="props">
          <q-badge v-if="!!props.value" color="blue" rounded><q-icon name="check" size="xs" /></q-badge>
        </q-td>
      </template>
    </q-table>
    <user-show v-if="selectedRows[0]" :model-value="selectedRows[0]" @close="selectedRows = []" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import UserShow from 'pages/entities/user/UserShow.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import { format } from 'src/util/format';

const baseApiUrl = '/api/users';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const organizations = ref([]);
const route = useRoute();
const router = useRouter();
const store = useAuthenticationStore();
const { t } = useI18n();
const selectedRows = ref([]);

const columns = computed(() => [
  {
    align: 'left',
    filter: hasRoleAdmin
      ? {
          field: 'orgId',
          type: 'enum',
          values: organizations.value.map(org => ({
            label: org.displayName,
            value: org.orgId
          }))
        }
      : false,
    format: (_value, row) => organizations.value.find(org => org.orgId === row.orgId),
    headerClasses: hasRoleAdmin ? '' : 'hidden',
    label: t('afaktoApp.datastream.organization'),
    name: 'orgId',
    sortable: true
  },
  {
    align: 'left',
    field: 'login',
    filter: { type: 'text' },
    label: t('afaktoApp.user.login'),
    name: 'login',
    sortable: true
  },
  {
    align: 'left',
    field: 'email',
    filter: { type: 'text' },
    label: t('afaktoApp.user.email'),
    name: 'email',
    sortable: true
  },
  {
    align: 'left',
    field: 'firstName',
    filter: { type: 'text' },
    label: t('afaktoApp.user.firstName'),
    name: 'firstName',
    sortable: true
  },
  {
    align: 'left',
    field: 'lastName',
    filter: { type: 'text' },
    label: t('afaktoApp.user.lastName'),
    name: 'lastName',
    sortable: true
  },
  {
    field: 'createdDate',
    filter: { type: 'date' },
    format: value => format(value),
    label: t('afaktoApp.user.createdDate'),
    name: 'createdDate',
    sortable: true
  },
  {
    align: 'center',
    field: 'activated',
    filter: { type: 'boolean' },
    label: t('afaktoApp.user.activated'),
    name: 'activated',
    sortable: true
  },
  {
    align: 'left',
    field: 'authorities',
    filter: {
      optionLabel: item => t(`afaktoApp.authority.values.${item}.label`),
      optionValue: item => item,
      type: 'select',
      url: '/api/authorities'
    },
    format: value =>
      value?.map(authority => t(`afaktoApp.authority.values.${authority}.label`)).join(', ') ||
      t('afaktoApp.authority.values.ROLE_READER.label'),
    label: t('afaktoApp.user.authorities'),
    name: 'authorities',
    sortable: true
  }
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'createdDate',
  descending: route.query.descending !== 'false',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  const response = await api.get(baseApiUrl, {
    params: {
      page: page - 1,
      size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
      sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
      ...filtersToQueryParams(filters.value)
    }
  });
  rows.value = response.data;

  pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
  router.replace({ query: { page, sortBy, descending, rowsPerPage } });
};

onMounted(async () => {
  if (!hasRoleAdmin) return;
  organizations.value = (await api.get('/api/organizations')).data;
});
</script>
