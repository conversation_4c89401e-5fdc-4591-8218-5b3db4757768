<template>
  <q-drawer overlay show-if-above side="right">
    <header>
      <q-btn class="buttonNeutral" icon="close" @click="$emit('close')" />

      <q-item-section>
        <q-item-label class="show-label">
          <q-icon name="o_person" />
          {{ $t('afaktoApp.user.detail.title') }}
        </q-item-label>
        <q-item-label>
          <h3>{{ modelValue.firstName }} {{ modelValue.lastName }}</h3>
        </q-item-label>
      </q-item-section>
    </header>

    <q-list>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.user.login') }}</q-item-section>
        <q-item-section avatar>{{ modelValue.login }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.user.email') }}</q-item-section>
        <q-item-section avatar class="self-center">{{ modelValue.email }}</q-item-section>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.user.authorities') }}</q-item-section>
        <div class="row q-gutter-sm">
          <q-item-section v-for="auth in modelValue.authorities" :key="auth" class="show-label" avatar>
            {{ auth.slice(5) }}
            <q-tooltip>
              {{ $t(`afaktoApp.authority.values.${auth}.help`) }}
            </q-tooltip>
          </q-item-section>
        </div>
      </q-item>
      <q-item>
        <q-item-section>{{ $t('afaktoApp.user.companies') }}</q-item-section>
        <div class="column q-py-sm">
          <q-item-section v-for="company in modelValue.companies" :key="company.id" avatar class="items-end">
            {{ company.name }}
          </q-item-section>
        </div>
      </q-item>
      <q-item v-if="modelValue.preferences?.REFERENCE_CURRENCY">
        <q-item-section>{{ $t('afaktoApp.user.me.referenceCurrency') }}</q-item-section>
        <q-item-section avatar>{{ modelValue.preferences?.REFERENCE_CURRENCY }}</q-item-section>
      </q-item>
    </q-list>

    <footer>
      <q-btn class="buttonBrand" icon="edit" :label="$t('entity.action.edit')" :to="`/users/${modelValue.id}`" />
    </footer>
  </q-drawer>
</template>

<script setup>
defineEmits(['close']);

defineProps({ modelValue: { type: Object, required: true } });
</script>
