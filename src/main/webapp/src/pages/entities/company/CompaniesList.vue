<template>
  <q-page>
    <q-toolbar>
      <h1>{{ $t('afaktoApp.company.home.title') }}</h1>

      <q-space />

      <div class="buttonDiv">
        <rows-export
          :base-api-url="baseApiUrl"
          class="buttonNeutral"
          :columns="columns"
          :filters="filters"
          :pagination="pagination"
          :visible-columns="visibleColumns"
        />

        <div class="btn-separator" />

        <q-btn class="buttonBrand" icon="add" :label="$t('afaktoApp.company.home.createLabel')" to="/companies/new" />
      </div>
    </q-toolbar>

    <q-toolbar>
      <columns-filtering v-model="filters" :columns="columns.filter(col => col.filter)" />
      <columns-visibility v-model="visibleColumns" :columns="columns" />
    </q-toolbar>

    <q-table
      v-model:pagination="pagination"
      binary-state-sort
      :columns="columns"
      row-key="id"
      :rows="rows"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      v-model:selected="selectedRows"
      @request="onRequest"
      @row-click="(_event, row) => (selectedRows = [row])"
    >
      <template #body-cell-orgId="props">
        <q-td v-if="hasRoleAdmin" align="left">
          <q-avatar class="on-left" size="36px">
            <img v-if="props.value?.logoUrl" :alt="props.value?.displayName" :src="props.value?.logoUrl" />
          </q-avatar>
          {{ props.value?.displayName }}
        </q-td>
      </template>
      <template #body-cell-numberType="props">
        <q-td :props="props">
          <q-badge v-if="props.row.numberType" color="blue">{{ props.row.numberType.replaceAll('_', ' ') }}</q-badge>
        </q-td>
      </template>
      <template #body-cell-number="props">
        <q-td :props="props">
          <template
            v-if="props.row.number && props.row.numberType === 'SIREN' && /^\d+$/.test(props.row.number) && props.row.number.length == 9"
          >
            {{ parseInt(props.row.number).toLocaleString('fr') }}
          </template>
          <template v-else>{{ props.row.number }}</template>
        </q-td>
      </template>
    </q-table>

    <companies-show v-if="selectedRows[0]" :model-value="selectedRows[0]" @close="selectedRows = []" />
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import CompaniesShow from 'pages/entities/company/CompaniesShow.vue';
import { NUMBER_TYPES } from 'src/constants/numberType';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { filtersToQueryParams } from 'src/util/filtering';
import useNotifications from 'src/util/useNotifications';

const baseApiUrl = '/api/companies';
const hasRoleAdmin = useAuthenticationStore().hasRoleAdmin;
const { notifyError } = useNotifications();
const organizations = ref([]);
const route = useRoute();
const router = useRouter();
const selectedRows = ref([]);
const store = useAuthenticationStore();
const { t } = useI18n();

const columns = computed(() => [
  {
    align: 'left',
    filter: hasRoleAdmin
      ? {
          field: 'orgId',
          type: 'enum',
          values: organizations.value.map(org => ({
            label: org.displayName,
            value: org.orgId
          }))
        }
      : false,
    format: (_value, row) => organizations.value.find(org => org.orgId === row.orgId),
    headerClasses: hasRoleAdmin ? '' : 'hidden',
    label: t('afaktoApp.datastream.organization'),
    name: 'orgId',
    sortable: true
  },
  {
    align: 'left',
    field: 'code',
    filter: { type: 'text' },
    label: t('afaktoApp.company.code'),
    name: 'code',
    sortable: true
  },
  {
    align: 'left',
    field: 'name',
    filter: { type: 'text' },
    label: t('afaktoApp.company.name'),
    name: 'name',
    sortable: true
  },
  {
    classes: 'type',
    field: 'numberType',
    filter: {
      type: 'enum',
      values: NUMBER_TYPES.map(numberType => ({
        label: numberType.replaceAll('_', ' '),
        value: numberType
      }))
    },
    headerClasses: 'type',
    label: t('afaktoApp.company.numberType'),
    name: 'numberType',
    sortable: true
  },
  {
    align: 'left',
    field: 'number',
    filter: { type: 'text' },
    label: t('afaktoApp.company.number'),
    sortable: true,
    name: 'number'
  }
]);

const visibleColumns = ref([]);
const filters = ref({});
watch(filters, () => onRequest({ pagination: pagination.value }), { deep: true });

const pagination = ref({
  sortBy: route.query.sortBy || 'name',
  descending: route.query.descending === 'true',
  page: Number.parseInt(route.query.page || 1),
  rowsPerPage: store._account.preferences.ROWS_PER_PAGE || 25,
  rowsNumber: 15
});

const rows = ref([]);

const onRequest = async props => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  try {
    const response = await api.get(baseApiUrl, {
      params: {
        page: page - 1,
        size: rowsPerPage === 0 ? pagination.value.rowsNumber : rowsPerPage,
        sort: `${sortBy},${descending ? 'desc' : 'asc'}`,
        ...filtersToQueryParams(filters.value)
      }
    });
    rows.value = response.data;

    pagination.value = { rowsNumber: response.headers['x-total-count'], page, rowsPerPage, sortBy, descending };
    router.replace({ query: { page, sortBy, descending, rowsPerPage } });
  } catch (error) {
    notifyError(error);
  }
};

onMounted(async () => {
  if (!hasRoleAdmin) return;
  organizations.value = (await api.get('/api/organizations')).data;
});
</script>
