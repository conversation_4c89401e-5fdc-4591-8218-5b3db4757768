<template>
  <q-page v-if="health">
    <div class="row items-start q-gutter-md q-pa-md">
      <q-card>
        <q-card-section class="text-center">
          {{ $t('health.indicator.diskSpace') }}
        </q-card-section>
        <q-card-section class="text-center">
          <q-badge :color="badgeColor(health.components.diskSpace.status)">
            {{ health.components.diskSpace.status }}
          </q-badge>
        </q-card-section>
        <q-card-section>
          <q-field
            v-for="item in ['total', 'free', 'threshold']"
            :key="item"
            :label="item"
            readonly
            borderless
            stack-label
            style="width: 100px"
          >
            <template #control>
              {{ humanStorageSize(health.components.diskSpace.details[item]) }}
            </template>
          </q-field>
        </q-card-section>
      </q-card>
      <q-card>
        <q-card-section class="text-center">
          {{ $t('health.indicator.db') }}
        </q-card-section>
        <q-card-section class="text-center">
          <q-badge :color="badgeColor(health.components.db.status)">
            {{ health.components.db.status }}
          </q-badge>
        </q-card-section>
        <q-card-section>
          <q-field
            v-for="item in ['database', 'validationQuery']"
            :key="item"
            :label="item"
            readonly
            borderless
            stack-label
            style="width: 100px"
          >
            <template #control>
              {{ health.components.db.details[item] }}
            </template>
          </q-field>
        </q-card-section>
      </q-card>

      <q-card>
        <q-card-section class="text-center">
          {{ $t('health.indicator.bankConnectors') }}
        </q-card-section>
        <q-card-section class="text-center">
          <q-badge :color="badgeColor(health.components.bankConnectors.status)">
            {{ health.components.bankConnectors.status }}
          </q-badge>
        </q-card-section>
        <q-card-section>
          <q-field v-for="item in ['BNP', 'SOCGEN']" :key="item" :label="item" readonly borderless stack-label style="width: 100px">
            <template #control>
              {{ health.components.bankConnectors.details[item] }}
            </template>
          </q-field>
        </q-card-section>
      </q-card>

      <q-card v-for="item in ['ping', 'livenessState', 'readinessState']" :key="item">
        <q-card-section class="text-center">
          {{ $t(`health.indicator.${item}`) }}
        </q-card-section>
        <q-card-section class="text-center">
          <q-badge :color="badgeColor(health.components[item].status)">
            {{ health.components[item].status }}
          </q-badge>
        </q-card-section>
      </q-card>

      <q-card>
        <q-card-section class="text-center">
          {{ $t('health.indicator.exchangeRates') }}
        </q-card-section>

        <div class="text-right" title="ECB">
          <a href="https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml">
            <img alt="ECB" class="vertical-middle" height="24" src="https://www.ecb.europa.eu/shared/img/logo/logo_only.svg" />
          </a>
          <q-icon class="on-right" name="o_currency_exchange" />
          3 hours
        </div>
        <div class="text-right" title="IMF">
          <a href="https://www.imf.org/external/np/fin/data/rms_mth.aspx">
            <img alt="IMF" class="vertical-middle" height="24" src="https://www.imf.org/assets/imf/images/footer/IMF_seal.svg" />
          </a>
          <q-icon class="on-right" name="o_currency_exchange" />
          6 hours
        </div>

        <table class="q-table">
          <thead>
            <tr>
              <th><q-icon class="text-h6" name="o_currency_exchange" /></th>
              <th v-for="currency in currencies" :key="currency">{{ currency }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="to in currencies" :key="to">
              <th>{{ to }}</th>
              <td v-for="from in currencies" :key="from" class="text-right" :title="exchangeRates[to][from]">
                {{
                  $n(exchangeRates[to][from], 'currency', {
                    currency: currency,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 4,
                    useGrouping: true
                  })
                }}
              </td>
            </tr>
          </tbody>
        </table>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { api } from 'boot/axios';
import { format } from 'quasar';
import { ref } from 'vue';

const badgeColor = value => (value === 'UP' ? 'green' : 'red');
const currencies = ref([]);
const exchangeRates = ref({});
const { humanStorageSize } = format;
const health = ref();

const getExchangeRates = async currency => (await api.get(`/api/contracts/exchangeRates/${currency}`)).data;

const fetchHealth = async () => {
  health.value = (await api.get('/management/health')).data;

  currencies.value = (await api.get('/api/contracts/currencies')).data;

  currencies.value.forEach(currency => getExchangeRates(currency).then(data => (exchangeRates.value[currency] = data)));
};

fetchHealth();
</script>
