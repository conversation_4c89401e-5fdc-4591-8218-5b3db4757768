<template>
  <b-input v-model="modelValue.name" :label="$t('afaktoApp.contact.name')" :readonly="readonly" />
  <b-input v-model="modelValue.phone" :label="$t('afaktoApp.contact.phone')" :readonly="readonly" type="tel" />
  <b-input v-model="modelValue.email" :label="$t('afaktoApp.contact.email')" :readonly="readonly" type="email" />
</template>

<script setup>
defineModel({ type: Object });

defineProps({
  readonly: <PERSON>olean
});
</script>
