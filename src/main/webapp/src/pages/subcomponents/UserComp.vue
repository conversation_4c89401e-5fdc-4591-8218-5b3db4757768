<template>
  <q-btn-group unelevated>
    <UserComments />

    <q-btn-dropdown class="q-px-md" color="neutralHighest" dropdown-icon="o_settings" fab-mini flat>
      <q-list v-if="entity">
        <q-item>
          <q-item-section>
            <q-btn-toggle
              v-model="entity.preferences.DARK_MODE"
              :icon="$q.dark.isActive ? 'dark_mode' : 'light_mode'"
              :options="[
                {
                  icon: 'o_hdr_auto',
                  label: $t('afaktoApp.user.me.mode.auto'),
                  value: 'auto'
                },
                {
                  icon: 'o_light_mode',
                  label: $t('afaktoApp.user.me.mode.light'),
                  value: 'false'
                },
                {
                  icon: 'o_dark_mode',
                  label: $t('afaktoApp.user.me.mode.dark'),
                  value: 'true'
                }
              ]"
              no-wrap
              spread
              :text-color="$q.dark.isActive ? 'white' : 'black'"
            />
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <q-btn-toggle
              v-model="entity.preferences.DENSE_MODE"
              :options="[
                {
                  icon: 'o_table_chart',
                  label: 'normal',
                  value: 'false'
                },
                {
                  icon: 'o_view_compact',
                  label: 'dense',
                  value: 'true'
                }
              ]"
              no-wrap
              spread
            >
            </q-btn-toggle>
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <b-currency v-model="entity.preferences.REFERENCE_CURRENCY" :label="$t('afaktoApp.user.me.referenceCurrency')" />
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>
            <q-select v-model="entity.preferences.ROWS_PER_PAGE" label="Elements per page" :options="[25, 50, 100]" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>

    <q-btn v-if="entity" fab-mini flat>
      <q-avatar size="36px">
        <img :src="entity.imageUrl" alt="" />
      </q-avatar>
      <q-menu>
        <q-list>
          <q-item>
            <q-item-section>
              <p class="no-margin">{{ entity.firstName }} {{ entity.lastName }}</p>
              <p class="no-margin">{{ entity.email }}</p>
            </q-item-section>
          </q-item>

          <q-separator />

          <q-item clickable v-ripple @click="() => authLogout(store)">
            <q-item-section avatar>
              <q-icon name="o_logout" />
            </q-item-section>
            <q-item-section>{{ $t('global.menu.account.logout') }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-btn>
  </q-btn-group>
</template>

<script setup>
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';
import { ref, watch } from 'vue';

import UserComments from 'pages/subcomponents/UserComments.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { authLogout } from 'src/util/authentication';
import useNotifications from 'src/util/useNotifications.js';

const { notifyError } = useNotifications();
const $q = useQuasar();
const store = useAuthenticationStore();

const baseApiUrl = '/api/account';
const entity = ref(store._account);

const updatePreferences = async () => {
  try {
    const response = await api.put(baseApiUrl, entity.value);
    store._account = response.data;
    if (store._account && entity.value) entity.value.version = store._account.version;
  } catch (error) {
    notifyError(error);
  }
};

watch(
  () => entity.value?.preferences?.DARK_MODE,
  value => $q.dark.set(value === 'auto' ? 'auto' : value === 'true')
);

watch(
  () => entity.value?.preferences,
  () => updatePreferences(),
  { deep: true }
);
</script>
