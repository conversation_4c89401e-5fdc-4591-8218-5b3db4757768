<template>
  <q-list bordered dense>
    <template v-if="typeof modelValue === 'string'">{{ modelValue }}</template>

    <q-item v-else v-for="(value, key) in modelValue" :key="key" style="min-height: initial">
      <template v-if="isObject(value)">
        <q-item-section>
          <q-item-label>{{ key }}</q-item-label>
          <json-viewer :model-value="value" />
        </q-item-section>
      </template>

      <template v-else-if="Array.isArray(value)">
        <q-item-section>
          <q-item-label>{{ key }}</q-item-label>
          <json-viewer v-for="item in value" :key="item" :model-value="item" />
        </q-item-section>
      </template>

      <template v-else>
        <q-item-section side top>{{ key }}</q-item-section>
        <q-item-section>{{ value }}</q-item-section>
      </template>
    </q-item>
  </q-list>
</template>

<script setup>
defineModel({ type: Object });

const isObject = value => value && typeof value === 'object' && !Array.isArray(value);
</script>

<style scoped>
.q-item-label {
  word-break: break-word;
}
</style>
