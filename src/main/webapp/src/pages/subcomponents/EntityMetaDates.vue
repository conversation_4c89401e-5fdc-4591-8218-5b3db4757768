<template>
  <table class="entity-meta">
    <tbody>
      <tr title="Version 0">
        <td align="right">{{ $t('global.field.createdDate') }}</td>
        <td>{{ format(entity.createdDate, 'dd/MM/yyyy HH:mm') }}</td>
        <template v-if="entity.createdBy != 'system'">
          <td align="right">{{ $t('global.field.by') }}</td>
          <td>👤 {{ entity.createdBy }}</td>
        </template>
      </tr>
      <tr v-if="entity.version" :title="`Version ${entity.version}`">
        <td align="right">{{ $t('global.field.lastModifiedDate') }}</td>
        <td>{{ format(entity.lastModifiedDate, 'dd/MM/yyyy HH:mm') }}</td>
        <template v-if="entity.lastModifiedBy != 'system'">
          <td align="right">{{ $t('global.field.by') }}</td>
          <td>👤 {{ entity.lastModifiedBy }}</td>
        </template>
      </tr>
    </tbody>
  </table>
</template>

<script setup>
import { format } from 'src/util/format';

defineProps({
  entity: {
    type: Object,
    required: true
  }
});
</script>

<style scoped>
td {
  white-space: nowrap;
}
</style>
