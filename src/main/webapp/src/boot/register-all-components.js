import VueApexCharts from 'vue3-apexcharts';

// Native replacements for lodash functions
const camelCase = str => str.replace(/[-_]([a-z])/g, g => g[1].toUpperCase());
const upperFirst = str => str.charAt(0).toUpperCase() + str.slice(1);
export default async ({ app }) => {
  // Use Vite's import.meta.glob instead of webpack's require.context
  const components = import.meta.glob('../components/**/*.vue');

  // Register all components
  for (const [path, importFn] of Object.entries(components)) {
    const fileName = path.split('/').pop();

    if (/[A-Z]\w+\.(vue)$/.test(fileName)) {
      const componentName = upperFirst(camelCase(fileName.replace(/\.\w+$/, '')));

      const component = await importFn();
      app.component(componentName, component.default);
    }
  }

  // Register ApexCharts globally
  app.use(VueApexCharts);
};
