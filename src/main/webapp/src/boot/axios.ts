import axios from 'axios';
import type { AxiosError, AxiosResponse } from 'axios';

import { useAuthenticationStore } from 'src/stores/authentication-store';
import { AUTH_PATH, authLogout } from 'src/util/authentication';

export const api = axios.create({});
const AUTO_LOGOUT_TIME = 29 * 60 * 1000;

// Function to toggle loading state
function toggleLoadingState(isLoading: boolean): void {
  document.body.classList.toggle('loading', isLoading);
}

function manageAutoLogoutTimer() {
  const authStore = useAuthenticationStore();

  if (authStore._logoutTimer) {
    clearTimeout(authStore._logoutTimer);
  }

  authStore._logoutTimer = setTimeout(() => {
    authLogout(authStore);
  }, AUTO_LOGOUT_TIME);
}

// Request interceptor to block double button clicks
function blockDoubleButtonClicks(config: any): any {
  toggleLoadingState(true);
  return config;
}

// Response interceptor to handle loading state and errors
function handleResponse(response: AxiosResponse): AxiosResponse {
  toggleLoadingState(false);
  manageAutoLogoutTimer();
  return response;
}

function handleError(error: AxiosError): Promise<void> | undefined {
  toggleLoadingState(false);

  if (!error?.response || error.response.status === 401) {
    window.location.href = AUTH_PATH;

    // Return a resolved promise to stop further error handling
    return Promise.resolve();
  }

  return Promise.reject(error);
}

api.interceptors.request.use(blockDoubleButtonClicks, handleError);
api.interceptors.response.use(handleResponse, handleError);
