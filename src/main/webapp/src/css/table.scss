main > .q-table__container {
  padding-bottom: 2.5em;

  .q-table__bottom {
    background-color: $backgroundSecondary;
    bottom: 0;
    left: 57px;
    min-height: auto;
    position: fixed;
    right: 0;
    z-index: 1;
    .body--dark & {
      background-color: $backgroundSecondaryDark;
      border-color: $borderSecondaryDark;
    }
    @media (max-width: $breakpoint-sm-max) {
      left: 0;
    }
  }
}

table.q-table {
  th,
  td {
    &.address_country,
    &.boolean {
      .q-icon,
      .fi {
        margin-left: -0.4em;
        margin-right: -0.4em;
      }
    }
    &.boolean {
      padding: 0 !important;
    }
    &.numberType,
    &.type {
      padding-right: 0;
    }
  }

  // Truncate large columns
  td.text-left:not(.boolean):not(.address_country):not(:last-child) {
    max-width: 10em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &:hover {
      background-color: $surface;
      border-radius: 4px;
      max-width: initial;
      position: absolute;
      z-index: 10;
    }
    .q-table--dark &:hover {
      background-color: $backgroundPrimaryDark;
    }
  }
}

th,
td {
  border-color: $borderSecondary;
  .q-table--dark & {
    border-color: $borderSecondaryDark;
  }
}

td.table-separator {
  background: $backgroundSecondary;
  padding: 0 !important;
  width: 1px;
  .body--dark & {
    background: $backgroundSecondaryDark;
  }
}
