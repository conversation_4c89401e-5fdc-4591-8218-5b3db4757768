{"afaktoApp": {"NumberType": {"AD": {"NATIONAL_REGISTRATION_NUMBER": {"label": "NO N.I.F", "regex": "^([A-Z]-\\d{6}-[A-Z]|[A-Z]\\d{6}[A-Z]|[A-Z]\\d{6}|\\d{6}[A-Z])$"}}, "AL": {"NATIONAL_REGISTRATION_NUMBER": {"label": "FISCAL NUMBER"}, "VAT": {"label": "TVA"}}, "AM": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}, "VAT": {"label": "INN (TVA)"}}, "AR": {"NATIONAL_REGISTRATION_NUMBER": {"label": "INGRESOS BRUTOS N°"}}, "AR1": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR2": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR3": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR4": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR5": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR6": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AR7": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}}, "AT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "HANDELREGISTER NUM.", "regex": "FN \\d{6} [a-z]"}, "VAT": {"label": "UID-Nummer", "regex": "ATU\\d{8}"}}, "AU": {"NATIONAL_REGISTRATION_NUMBER": {"label": "ACN", "regex": "^\\d{9}$"}}, "AW": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CRIB"}}, "AZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMER"}, "VAT": {"label": "INN (TVA)"}}, "BA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RB REGISTARSKI BROJ", "regex": "\\b\\d{13}\\b|\\b\\d{1,4}-\\d{1,6}\\b"}}, "BD": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMER"}}, "BE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "ONSS Identifier", "regex": "\\b\\d{7}\\b|\\b\\d{9}\\b"}, "VAT": {"label": "IDENT.FISCAL TVA/BTW", "regex": "\\bBE\\d{10}\\b"}}, "BF": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "FISCAL NUMBER"}}, "BG": {"NATIONAL_REGISTRATION_NUMBER": {"label": "NATIONAL ID", "regex": "^\\d{9}$"}, "VAT": {"label": "TVA", "regex": "^(BG)?\\d{9,10}$"}}, "BH": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "BJ": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "BN": {"NATIONAL_REGISTRATION_NUMBER": {"label": "INCORPORATION NUMBER"}}, "BO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "BR": {"NATIONAL_REGISTRATION_NUMBER": {"label": "N.I.R.E."}}, "BT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "BW": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REG: CERTIFICATE"}}, "BY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "EGR NUM DE REGISTRE"}, "VAT": {"label": "UNN/NUM. OF TAXPAYER"}}, "CA": {"DUNS_BRADSTREET": {"label": "D-U-N-S®", "regex": "^\\d{9}$"}}, "CH": {"COMMERCIAL_REGISTER": {"label": "Commercial Registered N°", "regex": "^CH-\\d{3}\\.\\d\\.\\d{3}\\.\\d{3}-\\d$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Enterprise Identification Number", "regex": "^CHE-\\d{3}\\.\\d{3}\\.\\d{3}$"}, "VAT": {"label": "VAT Number", "regex": "^CHE\\d{9}$"}}, "CI": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "COMPTE CONTRIBUABLE"}}, "CL": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.T."}}, "CM": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "VAT": {"label": "VAT number"}}, "CN": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Unified Social Credit Code"}, "VAT": {"label": "VAT NUMBER"}}, "CO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "N.I.T."}}, "CR": {"VAT": {"label": "CEDULA JURIDICA"}}, "CY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTERED NUMBER"}, "VAT": {"label": "VAT NUMBER"}}, "CZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CR CISLO REGISTRACE", "regex": "^[A-Z][a-z]? \\d+$"}, "VAT": {"label": "DIC    N. DE TVA", "regex": "^CZ\\d{8,10}$"}}, "DE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "commercial register number", "regex": "^HR[AB]\\s\\d+\\s(?:[A-Z]{1,3}\\s)?\\d{5}$"}, "VAT": {"label": "VAT number", "regex": "^DE\\d{9}$"}}, "DJ": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "DK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CVR number", "regex": "\\b\\d{8}\\b"}, "VAT": {"label": "VAT NUMBER", "regex": "\\bDK\\d{8}\\b"}}, "DN1": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "DO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.N.C."}}, "DZ": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Tax Number"}}, "EC": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "EE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}, "VAT": {"label": "VAT NUMBER"}}, "EG": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "VAT": {"label": "Tax Identification Number"}}, "ES": {"NATIONAL_REGISTRATION_NUMBER": {"label": "NO C.I.F / NIF", "regex": "^(?:ES)?[A-Z][0-9]{7}[A-Z0-9]$"}}, "FI": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^\\d{8}$"}, "VAT": {"label": "VAT - NUMBER", "regex": "^FI\\d{8}$"}}, "FJ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "FR": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER", "regex": "^RCS\\s+[A-Za-zÀ-ÿ\\-']+\\s+\\d(?:\\s*\\d){8}$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "SIREN", "regex": "^\\d{9}$"}, "VAT": {"label": "TVA", "regex": "^FR[A-Z0-9]{2}\\d{9}$"}}, "GA": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "STATISTIC NUMBER"}}, "GB": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^\\d{8}$"}, "VAT": {"label": "VAT Registration No.", "regex": "^GB\\d{9}$"}}, "GE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "OKPO"}, "VAT": {"label": "INN TVA"}}, "GH": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Tax Identification Number"}}, "GR": {"COMMERCIAL_REGISTER": {"label": "GEMI N°", "regex": "^\\d{12}$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^\\d{6}/\\d{3}/[A-ZΑ-Ω]{1,2}/\\d{2}/\\d{2,4}$"}, "VAT": {"label": "TVA/AFM", "regex": "^EL\\d{9}$"}}, "GT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "N.I.T."}}, "HK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "COMPANY REG NUMBER"}}, "HN": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.T.N."}}, "HR": {"NATIONAL_REGISTRATION_NUMBER": {"label": "MBSTS (REG NUMBER)", "regex": "^\\d{9}$"}, "VAT": {"label": "BZS (TVA)", "regex": "^\\d{11}$"}}, "HU": {"NATIONAL_REGISTRATION_NUMBER": {"label": "BEJEGYZESI SZAM", "regex": "^\\d{10}$"}, "VAT": {"label": "A ADOSZAM (TVA)", "regex": "^\\d{8}-\\d{1}-\\d{2}$"}}, "ID": {"COMMERCIAL_REGISTER": {"label": "TDP CY REGISTRATION"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "NPWP TAX REGISTRATION"}}, "IE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^(EI\\d{6}|\\d{1,6})$"}, "VAT": {"label": "VAT REGISTRATION NO", "regex": "^IE\\d{7}[A-Z]{1,2}$"}}, "IL": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^[0256]\\d{8}$"}}, "IN": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "IQ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Registration number"}}, "IR": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NBR."}, "VAT": {"label": "TAX REGISTRATION NBR"}}, "IS": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "IT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CODICE FISCALE", "regex": "^(?:[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]|[0-9]{11})$"}, "VAT": {"label": "TVA/N'IVA", "regex": "^IT[0-9]{11}$"}}, "JM": {"NATIONAL_REGISTRATION_NUMBER": {"label": "T.R.N."}}, "JO": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "National ID"}}, "JP": {"NATIONAL_REGISTRATION_NUMBER": {"label": "TEIKOKU NUMBER"}}, "KE": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "KG": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RC (REG NUMBER)"}, "VAT": {"label": "INN (TVA)"}}, "KH": {"NATIONAL_REGISTRATION_NUMBER": {"label": "INCORPORATION NUMBER"}}, "KR": {"COMMERCIAL_REGISTER": {"label": "BUSINESS REG. NO.", "regex": "^\\d{3}-\\d{2}-\\d{5}$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "CORPORATE REG. NO.", "regex": "^\\d{6}-\\d{7}$"}}, "KSV": {"NATIONAL_REGISTRATION_NUMBER": {"label": "MBSTS (REG NUMBER)"}, "VAT": {"label": "BZS (TVA)"}}, "KW": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Kuwait Chamber"}}, "KZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "BIN (Business Identification Number)", "regex": "^\\d{12}$"}, "VAT": {"label": "Fiscal ID (RNN)"}}, "LA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Incorporation number"}}, "LB": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "LI": {"COMMERCIAL_REGISTER": {"label": "FL-number"}, "VAT": {"label": "IDENTIFIANT TVA"}}, "LK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "LS": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION CERTIFICATE"}}, "LT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}, "VAT": {"label": "VAT NUMBER"}}, "LU": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER", "regex": "^[AB]\\d{4,7}$"}, "VAT": {"label": "NUMERO DE TVA", "regex": "^LU\\s\\d{8,9}$"}}, "LV": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "VAT": {"label": "VAT NUMBER"}}, "LY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Registration number"}}, "MA": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "CODE TVA"}}, "MC": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "S.S.E.E."}}, "MD": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RC (REG. NUMBER)"}, "VAT": {"label": "FISCAL (TVA)"}}, "ME": {"NATIONAL_REGISTRATION_NUMBER": {"label": "MBSTS (REG. NUMBER)"}, "VAT": {"label": "MB/PB/BZS (TVA)"}}, "MG": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "STATISTIC NUMBER"}}, "MH": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "MK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTER NUMBER"}, "VAT": {"label": "FISCAL (TVA)"}}, "ML": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "MM": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "MN": {"NATIONAL_REGISTRATION_NUMBER": {"label": "INCORPORATION NUMBER"}}, "MO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "MR": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "MT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Trade License Number"}, "VAT": {"label": "VAT NUMBER"}}, "MU": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "MX": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.F.C."}}, "MY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "ROC NUMBER"}}, "NA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "NC": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "SIREN"}}, "NE": {"COMMERCIAL_REGISTER": {"label": "NO TRANSLATION (CVCompanyIdentifiers.RCS_NER)"}}, "NI": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "NL": {"NATIONAL_REGISTRATION_NUMBER": {"label": "KVK HANDELSREGISTER", "regex": "^\\d{8}$"}, "VAT": {"label": "BTW Nummer", "regex": "^NL\\d{9}B\\d{2}$"}}, "NO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}, "VAT": {"label": "VAT"}}, "NP": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "NZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Registration number"}}, "NZ1": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "OM": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER", "regex": "^(?:\\d{4,7}|\\d{1}/\\d{4,6}/\\d{1})$"}}, "PA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "PE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "PH": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRE S.E.C NV"}}, "PK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "SERIAL NUMBER", "regex": "^(?:\\d{7}-\\d|\\d{2}-\\d{2}-\\d{4}-\\d{3}-\\d{2}|\\d{7})$"}}, "PL": {"NATIONAL_REGISTRATION_NUMBER": {"label": "STATISTIC NUMBER", "regex": "\\b\\d{9}\\b"}, "VAT": {"label": "NIP NUM PLATNIKA VAT", "regex": "\\b\\d{10}\\b"}}, "PS": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "PT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "N. CONTRIBUINTE", "regex": "^PT\\d{9}$"}}, "PY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.C."}}, "QA": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "RO": {"NATIONAL_REGISTRATION_NUMBER": {"label": "European VAT"}, "VAT": {"label": "CF (TVA)"}}, "RS": {"NATIONAL_REGISTRATION_NUMBER": {"label": "MBSTS (REG. NUMBER)", "regex": "BD\\s\\d{5}"}, "VAT": {"label": "MB/PB/BZS (TVA)", "regex": "^\\d{9}"}}, "RU": {"NATIONAL_REGISTRATION_NUMBER": {"label": "O.G.R.N.", "regex": "^\\d{13}$"}, "VAT": {"label": "INN (TVA)", "regex": "^\\d{10}$"}}, "SA": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "VAT": {"label": "VAT Number"}}, "SC": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "SD": {"NATIONAL_REGISTRATION_NUMBER": {"label": "Registration number"}}, "SE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER", "regex": "^\\d{10}$"}, "VAT": {"label": "VAT - NUMBER", "regex": "^SE\\d{10}01$"}}, "SG": {"NATIONAL_REGISTRATION_NUMBER": {"label": "ROC NUMBER"}}, "SI": {"COMMERCIAL_REGISTER": {"label": "RS REG. DU COMMERCE", "regex": "^(\\d{1,9}([-/]\\d{1,9})*|\\d{1,9})$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "TAX NUMBER", "regex": "^\\d{8}$"}, "VAT": {"label": "DS N. DE TVA", "regex": "^(SI)\\d{8}$"}}, "SK": {"NATIONAL_REGISTRATION_NUMBER": {"label": "IR ISLO REGISTACIE", "regex": "^\\d{1,5}(-\\d{1,6}|/[A-Z]|/[A-Z]{1,2})$"}, "VAT": {"label": "VAT", "regex": "^SK\\d{10}$"}}, "SM": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CODICE FISCALE"}}, "SN": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "COMPTE CONTRIBUABLE"}}, "SV": {"NATIONAL_REGISTRATION_NUMBER": {"label": "N.I.T."}}, "SY": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "SZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REG : CERTIFICATE"}}, "TD": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "TG": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "TH": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION NUMBER"}}, "TJ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RC (REG. NUMBER)"}, "VAT": {"label": "INN (TVA)"}}, "TL": {"COMMERCIAL_REGISTER": {"label": "NPWP TAX REGISTRATIO"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "TDP CY REGISTRATION"}}, "TM": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RC (REG. NUMBER)"}, "VAT": {"label": "INN (TVA)"}}, "TN": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "UNIQUE IDENTIFIER"}}, "TR": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTERED NUMBER"}, "VAT": {"label": "TVA/VAT", "regex": "^\\d{10,11}$"}}, "TT": {"NATIONAL_REGISTRATION_NUMBER": {"label": "B.I.R."}}, "TW": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "UA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "STATISTIC NUMBER", "regex": "^\\d{8}$"}, "VAT": {"label": "UNN (TVA)", "regex": "^\\d{10}(\\d{2})?$"}}, "US": {"DUNS_BRADSTREET": {"label": "D-U-N-S®", "regex": "^\\d{9}$"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "BUSINESS REGISTRATION"}}, "US1": {"DUNS_BRADSTREET": {"label": "D-U-N-S®"}, "NATIONAL_REGISTRATION_NUMBER": {"label": "NO TRANSLATION (CVCompanyIdentifiers.RGN_US1)"}}, "UY": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.U.T."}}, "UZ": {"NATIONAL_REGISTRATION_NUMBER": {"label": "RC (REG. NUMBER)"}, "VAT": {"label": "INN (TVA)"}}, "VA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "CODICE FISCALE"}}, "VE": {"NATIONAL_REGISTRATION_NUMBER": {"label": "R.I.F."}}, "VN": {"COMMERCIAL_REGISTER": {"label": "BUSINESS REG. NO."}, "NATIONAL_REGISTRATION_NUMBER": {"label": "Tax Code", "regex": "^\\d{10}$"}}, "YE": {"COMMERCIAL_REGISTER": {"label": "TRADE REGISTER"}}, "ZA": {"NATIONAL_REGISTRATION_NUMBER": {"label": "TRUST", "regex": "^(IT\\d{1,6}/\\d{2,4}|\\d{4,6}/\\d{4})$"}, "VAT": {"label": "VAT Number", "regex": "^\\d{10}$"}}, "ZW": {"NATIONAL_REGISTRATION_NUMBER": {"label": "REGISTRATION CERTIFICATE"}}}}}