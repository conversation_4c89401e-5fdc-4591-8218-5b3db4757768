{"afaktoApp": {"address": {"home": {"title": "Adresses", "refreshListLabel": "Actualiser la liste", "createLabel": "<PERSON><PERSON><PERSON> une nouvelle adresse", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer une adresse", "search": "Recherche d'adresse", "notFound": "<PERSON><PERSON><PERSON> adresse trouvée"}, "created": "Une nouvelle adresse a été créée avec l'identifiant { param }", "updated": "L'adresse avec l'identifiant { param } a été mise à jour", "deleted": "L'adresse avec l'identifiant { param } a été supprimée", "delete": {"question": "Êtes-vous certain de vouloir supprimer l'adresse { id } ?"}, "detail": {"title": "<PERSON><PERSON><PERSON>"}, "id": "ID", "street": "Rue", "streetName": "Rue", "streetNumber": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "Code postal", "postalCodeFilter": "Filtrer par code postal", "city": "Ville", "stateProvince": "État/Province", "country": "Pays"}}}