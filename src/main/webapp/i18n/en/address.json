{"afaktoApp": {"address": {"home": {"title": "Addresses", "refreshListLabel": "Refresh list", "createLabel": "Create a new address", "createOrEditLabel": "Create or edit an address", "search": "Search for address", "notFound": "No addresses found"}, "created": "A new address is created with identifier { param }", "updated": "A address is updated with identifier { param }", "deleted": "A address is deleted with identifier { param }", "delete": {"question": "Are you sure you want to delete address { id }?"}, "detail": {"title": "Address"}, "id": "ID", "street": "Street", "streetName": "Street name", "streetNumber": "Street number", "postalCode": "Postal code", "postalCodeFilter": "Filter per postal code", "city": "City", "stateProvince": "State province", "country": "Country"}}}